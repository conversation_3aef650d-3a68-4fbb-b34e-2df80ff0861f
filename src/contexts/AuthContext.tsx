import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// User interface for Impression app
export interface User {
  id: string;
  email: string;
  name: string;
  photos: string[];
  bio?: string;
  age?: number;
  location?: {
    zone: string;
    coordinates?: { lat: number; lng: number };
  };
  membershipPlan: 'Basic' | 'Premium' | 'VIP';
  onboardingComplete: boolean;
  behavioralVector?: number[];
  connectionIntents: string[];
  voiceTone?: string;
  createdAt: Date;
  lastActive: Date;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Mock authentication functions - replace with Firebase/Supabase
  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock user data
      const mockUser: User = {
        id: '1',
        email,
        name: 'Demo User',
        photos: [],
        membershipPlan: 'Basic',
        onboardingComplete: false,
        connectionIntents: [],
        createdAt: new Date(),
        lastActive: new Date(),
      };
      
      setUser(mockUser);
      localStorage.setItem('impression_user', JSON.stringify(mockUser));
    } catch (error) {
      throw new Error('Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, name: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newUser: User = {
        id: Date.now().toString(),
        email,
        name,
        photos: [],
        membershipPlan: 'Basic',
        onboardingComplete: false,
        connectionIntents: [],
        createdAt: new Date(),
        lastActive: new Date(),
      };
      
      setUser(newUser);
      localStorage.setItem('impression_user', JSON.stringify(newUser));
    } catch (error) {
      throw new Error('Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setUser(null);
    localStorage.removeItem('impression_user');
  };

  const updateUser = async (userData: Partial<User>): Promise<void> => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('impression_user', JSON.stringify(updatedUser));
    }
  };

  // Check for existing session on mount
  useEffect(() => {
    const checkAuthState = () => {
      try {
        const storedUser = localStorage.getItem('impression_user');
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
        }
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('impression_user');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthState();
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
