# Impression - Resonance-Based Relationship App

An AI-assisted relational dynamics app that emphasizes emergent relationships through behavior rather than explicit declarations. This is not a dating app, but a resonance-based dynamic system where relationships emerge through behavioral patterns and authentic connections.

## 🌟 Core Philosophy

**Users are never forced into categories—they are revealed through pattern and reflection.**

Impression facilitates connections through:
- Behavioral vector analysis
- Privacy-first location abstraction
- Dynamic relationship formation
- Resonance-based matching algorithms

## 🚀 Features

### ✨ Implemented (MVP)
- **Landing Page** - Immersive introduction to the resonance-based philosophy
- **Authentication System** - Secure login/register with privacy-focused design
- **Responsive Design** - Works seamlessly on web and mobile
- **Privacy-First Architecture** - Location abstraction and anonymous interactions
- **Modern UI/UX** - Beautiful animations and intuitive interface

### 🔄 In Development (Week 1 Priority)
- **Onboarding Module** - Profile creation with behavioral intent capture
- **Resonance Map** - Abstract geographic interface with Bubble Grid
- **Profile View Tracking** - Behavioral signal detection during interactions
- **Matching Engine** - Dynamic threshold-based compatibility analysis
- **Dynamic Relationships** - Unit Profile formation and management

### 📋 Planned Features
- **AI NLP Layer** - Bio analysis and emotional alignment scoring
- **Advanced Analytics** - Behavioral pattern recognition
- **Mobile App** - Capacitor-based Android wrapper
- **Real-time Features** - Live interaction and matching

## 🛠 Tech Stack

- **Frontend**: React 19, TypeScript, Tailwind CSS
- **Animations**: Framer Motion
- **Routing**: React Router DOM
- **State Management**: React Query
- **Backend**: Firebase/Supabase (configurable)
- **Mobile**: Capacitor for Android
- **Build Tool**: Vite
- **UI Components**: Custom design system with shadcn/ui patterns

## 🏃‍♂️ Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

The app will be available at `http://localhost:5173`

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components
│   ├── ErrorBoundary.tsx
│   └── ProtectedRoute.tsx
├── contexts/           # React contexts
│   └── AuthContext.tsx
├── lib/               # Utility functions
│   └── utils.ts
├── pages/             # Page components
│   ├── LandingPage.tsx
│   ├── Login.tsx
│   ├── Register.tsx
│   ├── Onboarding.tsx
│   ├── ResonanceMap.tsx
│   ├── ProfileView.tsx
│   ├── Matches.tsx
│   └── Settings.tsx
├── types/             # TypeScript type definitions
│   └── index.ts
└── App.tsx            # Main app component
```

## 🎨 Design System

The app uses a custom design system built around the "Impression" brand:

- **Colors**: Pink, Purple, Indigo gradients on dark backgrounds
- **Typography**: Modern, clean fonts with gradient text effects
- **Animations**: Subtle, meaningful motion design
- **Components**: Consistent, accessible UI patterns

### Custom CSS Classes
- `.impression-gradient` - Brand gradient backgrounds
- `.impression-text-gradient` - Gradient text effects
- `.impression-card` - Styled card components
- `.impression-button` - Brand button styling

## 🔒 Privacy & Security

Impression is built with privacy as a core principle:

- **Location Abstraction**: Precise coordinates are never exposed
- **Anonymous Interactions**: Users remain anonymous until mutual interest
- **Behavioral Privacy**: All tracking is consent-based and transparent
- **Data Minimization**: Only necessary data is collected and stored

## 🧪 Development Guidelines

### Code Style
- Use TypeScript for all new code
- Follow React best practices and hooks patterns
- Implement responsive design for all components
- Write meaningful component and function names
- Add proper error handling and loading states

### Performance
- Optimize animations for 60fps on mid-range devices
- Implement lazy loading for heavy components
- Use React.memo for expensive re-renders
- Monitor bundle size and optimize imports

### Testing Strategy
- Unit tests for utility functions
- Integration tests for user flows
- Performance testing for animations
- Cross-device compatibility testing

## 🚀 Deployment

The app is designed for rapid deployment with multiple hosting options:

- **Web**: Vercel, Netlify, or Firebase Hosting
- **Mobile**: Google Play Store via Capacitor
- **Backend**: Firebase or Supabase for serverless architecture

## 📈 Roadmap

### Week 1 (MVP)
- [x] Project setup and authentication
- [x] Landing page and basic UI
- [ ] Onboarding flow
- [ ] Basic map interface
- [ ] Profile viewing with tracking

### Week 2-4 (Core Features)
- [ ] Behavioral vector engine
- [ ] Matching algorithms
- [ ] Dynamic relationship formation
- [ ] Mobile app packaging

### Month 2+ (Advanced Features)
- [ ] AI-powered bio analysis
- [ ] Advanced analytics dashboard
- [ ] Real-time features
- [ ] Global deployment

## 🤝 Contributing

This project follows the technical plan for rapid development. Key principles:

1. **Privacy First**: All features must respect user privacy
2. **Performance**: Smooth experience over feature completeness
3. **Philosophy**: Maintain the resonance-based approach
4. **Rapid Iteration**: Focus on MVP features first

## 📄 License

This project is part of the Impression app development initiative focused on creating authentic, resonance-based relationships through behavioral intelligence.

---

**Impression** - Where relationships emerge through resonance, not declarations.
