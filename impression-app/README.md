# Impression - AI-Assisted Relational Dynamics App

Impression is a revolutionary dating and connection app that emphasizes emergent relationships through behavioral tracking and vector-based matching. Unlike traditional dating apps that rely on explicit declarations, Impression uses AI to analyze user behavior and create meaningful connections based on authentic interaction patterns.

## 🌟 Core Philosophy

**Users are never forced into categories—they are revealed through pattern and reflection.**

Impression facilitates connections through:
- **10-Dimensional Behavioral Vectors**: Sophisticated behavioral analysis
- **Privacy-first location abstraction**: Zone-based matching without exposing exact locations
- **Dynamic relationship formation**: Multiple connection types (romantic, platonic, collaborative, creative)
- **Resonance-based matching algorithms**: AI-powered compatibility analysis

## 🚀 Features

### ✨ Fully Implemented
- **Landing Page** - Immersive introduction to the resonance-based philosophy
- **Authentication System** - Secure login/register with privacy-focused design
- **Complete Onboarding Flow** - Profile creation with behavioral intent capture
- **Resonance Map** - Interactive geographic interface with real-time user discovery
- **Profile View with Behavioral Tracking** - Comprehensive interaction monitoring
- **Behavioral Vector Engine** - 10-dimensional behavioral analysis system
- **Resonance Matching Engine** - AI-powered compatibility analysis
- **Dynamic Match System** - Progressive identity revelation with 48-hour windows
- **Privacy Controls** - Granular privacy settings and data management
- **NLP Engine** - Bio and voice tone analysis capabilities
- **Analytics Engine** - Comprehensive user behavior tracking
- **Mobile-Ready** - Capacitor integration for Android deployment
- **Comprehensive Testing** - Full test suite with Vitest

### 🎯 Key Technical Achievements
- **Behavioral Vector Processing**: Real-time analysis of user interactions
- **Device-Adaptive Tracking**: Optimized for different device capabilities
- **Progressive Identity Revelation**: Gradual disclosure based on mutual engagement
- **Privacy-Compliant Analytics**: Local processing with user control
- **Cross-Platform Deployment**: Web and mobile with shared codebase

## 🛠 Tech Stack

### Frontend Stack
- **React 19** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Router** for navigation
- **Capacitor** for mobile deployment

### Core Services
- **Behavioral Vector Engine**: Processes interaction data into 10D vectors
- **Resonance Matching Engine**: AI-powered compatibility analysis
- **NLP Engine**: Bio and voice tone analysis
- **Analytics Engine**: Comprehensive user behavior tracking

### Mobile & Testing
- **Capacitor** for cross-platform mobile apps
- **Vitest** for testing framework
- **Testing Library** for component testing
- **Android Studio** integration

## 🏃‍♂️ Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Android Studio (for mobile development)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd impression-app

# Install dependencies
npm install

# Start development server
npm run dev
```

### Development Scripts

```bash
# Web Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build

# Testing
npm run test         # Run tests in watch mode
npm run test:run     # Run tests once
npm run test:coverage # Run tests with coverage

# Mobile Development
npm run cap:build    # Build and sync with Capacitor
npm run cap:android  # Open Android Studio
npm run mobile:dev   # Development with live reload

# Deployment
npm run deploy:web     # Deploy web version
npm run deploy:android # Build Android APK
```

The app will be available at `http://localhost:5173`

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (Button, Card, Input, etc.)
│   ├── ErrorBoundary.tsx
│   └── ProtectedRoute.tsx
├── contexts/           # React contexts
│   └── AuthContext.tsx # Authentication and user management
├── lib/               # Utility functions
│   └── utils.ts       # Mathematical operations, validation, device detection
├── pages/             # Page components
│   ├── LandingPage.tsx    # Marketing and introduction
│   ├── Login.tsx          # User authentication
│   ├── Register.tsx       # User registration
│   ├── Onboarding.tsx     # Profile setup and intent capture
│   ├── ResonanceMap.tsx   # Interactive map with user discovery
│   ├── ProfileView.tsx    # Profile viewing with behavioral tracking
│   ├── Matches.tsx        # Match management and dynamics
│   └── Settings.tsx       # Privacy controls and preferences
├── services/          # Core business logic
│   ├── behavioralEngine.ts  # 10D vector processing
│   ├── matchingEngine.ts    # Resonance matching algorithms
│   ├── nlpEngine.ts         # Bio and voice analysis
│   └── analyticsEngine.ts   # User behavior tracking
├── test/              # Testing infrastructure
│   ├── setup.ts           # Test configuration
│   ├── utils.test.ts      # Utility function tests
│   └── behavioralEngine.test.ts # Core engine tests
├── types/             # TypeScript type definitions
│   └── index.ts       # Comprehensive type system
└── App.tsx            # Main app component
```

## 🎯 Behavioral Analysis

### 10-Dimensional Vector System
1. **Dwell Time**: Overall engagement duration (0-30s normalized)
2. **Scroll Velocity**: Interaction pace and rhythm
3. **Scroll Distance**: Content exploration depth
4. **Photo Focus**: Visual attention patterns (% of total time)
5. **Bio Focus**: Text content engagement (% of total time)
6. **Details Focus**: Information seeking behavior (% of total time)
7. **Interaction Depth**: Feature usage frequency (0-10 normalized)
8. **Return Behavior**: Revisit patterns (0-5 normalized)
9. **Engagement Intensity**: Overall interaction quality
10. **Scroll Consistency**: Behavioral pattern stability

### Match Types
- **First Flirt**: Romantic resonance detected (photo focus + dwell time)
- **First Meet**: Platonic connection emerging (bio focus + engagement)
- **First Collab**: Collaborative potential identified (details focus + bio)
- **First Sync**: Creative synchronicity found (engagement + consistency)
- **First Encounter**: General compatibility baseline

### Dynamic Threshold System
- **Base Threshold**: 0.72 similarity score
- **Density Adjustment**: Higher density areas require higher similarity
- **Device Adaptation**: Confidence adjusted based on device capabilities
- **Privacy Compliance**: All tracking requires explicit user consent

## 🎨 Design System

The app uses a custom design system built around the "Impression" brand:

- **Colors**: Pink, Purple, Indigo gradients on dark backgrounds
- **Typography**: Modern, clean fonts with gradient text effects
- **Animations**: Subtle, meaningful motion design
- **Components**: Consistent, accessible UI patterns

### Custom CSS Classes
- `.impression-gradient` - Brand gradient backgrounds
- `.impression-text-gradient` - Gradient text effects
- `.impression-card` - Styled card components
- `.impression-button` - Brand button styling

## 🔒 Privacy & Security

Impression is built with privacy as a core principle:

- **Location Abstraction**: Precise coordinates are never exposed
- **Anonymous Interactions**: Users remain anonymous until mutual interest
- **Behavioral Privacy**: All tracking is consent-based and transparent
- **Data Minimization**: Only necessary data is collected and stored

## 🧪 Development Guidelines

### Code Style
- Use TypeScript for all new code
- Follow React best practices and hooks patterns
- Implement responsive design for all components
- Write meaningful component and function names
- Add proper error handling and loading states

### Performance
- Optimize animations for 60fps on mid-range devices
- Implement lazy loading for heavy components
- Use React.memo for expensive re-renders
- Monitor bundle size and optimize imports

### Testing Strategy
- Unit tests for utility functions
- Integration tests for user flows
- Performance testing for animations
- Cross-device compatibility testing

## 🚀 Deployment

The app is designed for rapid deployment with multiple hosting options:

- **Web**: Vercel, Netlify, or Firebase Hosting
- **Mobile**: Google Play Store via Capacitor
- **Backend**: Firebase or Supabase for serverless architecture

## 📈 Roadmap

### Week 1 (MVP)
- [x] Project setup and authentication
- [x] Landing page and basic UI
- [ ] Onboarding flow
- [ ] Basic map interface
- [ ] Profile viewing with tracking

### Week 2-4 (Core Features)
- [ ] Behavioral vector engine
- [ ] Matching algorithms
- [ ] Dynamic relationship formation
- [ ] Mobile app packaging

### Month 2+ (Advanced Features)
- [ ] AI-powered bio analysis
- [ ] Advanced analytics dashboard
- [ ] Real-time features
- [ ] Global deployment

## 🤝 Contributing

This project follows the technical plan for rapid development. Key principles:

1. **Privacy First**: All features must respect user privacy
2. **Performance**: Smooth experience over feature completeness
3. **Philosophy**: Maintain the resonance-based approach
4. **Rapid Iteration**: Focus on MVP features first

## 📄 License

This project is part of the Impression app development initiative focused on creating authentic, resonance-based relationships through behavioral intelligence.

---

**Impression** - Where relationships emerge through resonance, not declarations.
