<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/impression-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#ec4899" />
    <meta name="description" content="Impression - Where relationships emerge through resonance, not declarations" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Impression - Resonance-Based Relationships" />
    <meta property="og:description" content="An AI-assisted relational dynamics app that emphasizes emergent relationships through behavior rather than explicit declarations" />
    <meta property="og:image" content="/impression-og.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="Impression - Resonance-Based Relationships" />
    <meta property="twitter:description" content="Where relationships emerge through resonance, not declarations" />
    <meta property="twitter:image" content="/impression-og.png" />

    <title>Impression - Resonance-Based Relationships</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
