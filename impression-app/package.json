{"name": "impression-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "cap:build": "npm run build && npx cap sync", "cap:android": "npm run cap:build && npx cap open android", "cap:run:android": "npm run cap:build && npx cap run android", "cap:sync": "npx cap sync", "mobile:dev": "npm run build && npx cap sync && npx cap run android --livereload --external", "deploy:web": "npm run build", "deploy:android": "npm run cap:build && cd android && ./gradlew assembleRelease"}, "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/geolocation": "^7.1.2", "@capacitor/haptics": "^7.0.1", "@capacitor/local-notifications": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.79.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.8.1", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.1.4"}}