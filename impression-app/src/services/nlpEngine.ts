import type { BioAnalysis, VoiceToneProfile } from '@/types';

export class NLPEngine {
  private static instance: NLPEngine;
  private apiKey: string | null = null;
  private baseUrl = 'https://openrouter.ai/api/v1';
  private analysisCache = new Map<string, BioAnalysis>();

  static getInstance(): NLPEngine {
    if (!NLPEngine.instance) {
      NLPEngine.instance = new NLPEngine();
    }
    return NLPEngine.instance;
  }

  /**
   * Initialize with API key
   */
  initialize(apiKey: string): void {
    this.apiKey = apiKey;
  }

  /**
   * Analyze bio text for emotional and tonal indicators
   */
  async analyzeBio(bioText: string): Promise<BioAnalysis> {
    // Check cache first
    const cacheKey = this.hashText(bioText);
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)!;
    }

    try {
      // For demo purposes, use local analysis
      // In production, this would call Open Router API
      const analysis = await this.performLocalAnalysis(bioText);

      // Cache the result
      this.analysisCache.set(cacheKey, analysis);

      return analysis;
    } catch (error) {
      console.error('Error analyzing bio:', error);
      // Return fallback analysis
      return this.getFallbackAnalysis(bioText);
    }
  }

  /**
   * Analyze voice tone from audio sample
   */
  async analyzeVoiceTone(audioData: string): Promise<VoiceToneProfile> {
    // This would integrate with speech analysis APIs
    // For now, return mock data
    return {
      humor: Math.random() * 0.4 + 0.3, // 0.3-0.7
      confidence: Math.random() * 0.4 + 0.4, // 0.4-0.8
      vulnerability: Math.random() * 0.6 + 0.2, // 0.2-0.8
      creativity: Math.random() * 0.5 + 0.3, // 0.3-0.8
      warmth: Math.random() * 0.4 + 0.4, // 0.4-0.8
      energy: Math.random() * 0.6 + 0.2, // 0.2-0.8
      audioSample: audioData,
      analysisTimestamp: new Date()
    };
  }

  /**
   * Compare two bio analyses for compatibility
   */
  compareBioCompatibility(analysisA: BioAnalysis, analysisB: BioAnalysis): number {
    const weights = {
      humor: 0.2,
      confidence: 0.15,
      vulnerability: 0.25,
      creativity: 0.2,
      tonePolarity: 0.2
    };

    let totalCompatibility = 0;
    let totalWeight = 0;

    // Compare each dimension
    Object.entries(weights).forEach(([dimension, weight]) => {
      const valueA = analysisA[dimension as keyof BioAnalysis] as number;
      const valueB = analysisB[dimension as keyof BioAnalysis] as number;

      if (typeof valueA === 'number' && typeof valueB === 'number') {
        // Calculate compatibility (1 - absolute difference)
        const compatibility = 1 - Math.abs(valueA - valueB);
        totalCompatibility += compatibility * weight;
        totalWeight += weight;
      }
    });

    return totalWeight > 0 ? totalCompatibility / totalWeight : 0.5;
  }

  /**
   * Local analysis fallback (simplified NLP)
   */
  private async performLocalAnalysis(bioText: string): Promise<BioAnalysis> {
    const words = bioText.toLowerCase().split(/\W+/).filter(w => w.length > 2);
    const sentences = bioText.split(/[.!?]+/).filter(s => s.trim().length > 0);

    // Humor indicators
    const humorWords = ['funny', 'laugh', 'joke', 'humor', 'hilarious', 'witty', 'sarcastic', 'comedy'];
    const humorScore = this.calculateWordScore(words, humorWords) * 0.7 +
                     (bioText.includes('😂') || bioText.includes('😄') ? 0.3 : 0);

    // Confidence indicators
    const confidenceWords = ['confident', 'strong', 'leader', 'achieve', 'success', 'ambitious', 'determined'];
    const confidenceScore = this.calculateWordScore(words, confidenceWords) * 0.8 +
                           (this.hasConfidentTone(sentences) ? 0.2 : 0);

    // Vulnerability indicators
    const vulnerabilityWords = ['feel', 'heart', 'emotional', 'sensitive', 'deep', 'meaningful', 'authentic'];
    const vulnerabilityScore = this.calculateWordScore(words, vulnerabilityWords) * 0.6 +
                              (this.hasVulnerableTone(sentences) ? 0.4 : 0);

    // Creativity indicators
    const creativityWords = ['creative', 'art', 'music', 'design', 'imagine', 'innovative', 'unique', 'original'];
    const creativityScore = this.calculateWordScore(words, creativityWords);

    // Tone polarity (positive vs negative)
    const positiveWords = ['love', 'happy', 'joy', 'amazing', 'wonderful', 'great', 'awesome', 'beautiful'];
    const negativeWords = ['hate', 'sad', 'terrible', 'awful', 'bad', 'worst', 'horrible', 'depressing'];
    const positiveScore = this.calculateWordScore(words, positiveWords);
    const negativeScore = this.calculateWordScore(words, negativeWords);
    const tonePolarity = Math.max(0, Math.min(1, 0.5 + (positiveScore - negativeScore)));

    // Extract keywords
    const keywords = this.extractKeywords(words);

    // Determine emotional tone
    const emotionalTone = this.determineEmotionalTone(humorScore, confidenceScore, vulnerabilityScore, tonePolarity);

    return {
      humor: Math.max(0, Math.min(1, humorScore)),
      confidence: Math.max(0, Math.min(1, confidenceScore)),
      vulnerability: Math.max(0, Math.min(1, vulnerabilityScore)),
      tonePolarity: Math.max(0, Math.min(1, tonePolarity)),
      creativity: Math.max(0, Math.min(1, creativityScore)),
      keywords,
      emotionalTone,
      analysisTimestamp: new Date()
    };
  }

  /**
   * Calculate word score based on keyword presence
   */
  private calculateWordScore(words: string[], keywords: string[]): number {
    const matches = words.filter(word => keywords.some(keyword => word.includes(keyword)));
    return Math.min(1, matches.length / Math.max(1, words.length * 0.1));
  }

  /**
   * Check for confident tone in sentences
   */
  private hasConfidentTone(sentences: string[]): boolean {
    const confidentPatterns = [
      /I am/i,
      /I will/i,
      /I can/i,
      /I know/i,
      /definitely/i,
      /absolutely/i
    ];

    return sentences.some(sentence =>
      confidentPatterns.some(pattern => pattern.test(sentence))
    );
  }

  /**
   * Check for vulnerable tone in sentences
   */
  private hasVulnerableTone(sentences: string[]): boolean {
    const vulnerablePatterns = [
      /I feel/i,
      /I hope/i,
      /I wish/i,
      /sometimes/i,
      /maybe/i,
      /I think/i
    ];

    return sentences.some(sentence =>
      vulnerablePatterns.some(pattern => pattern.test(sentence))
    );
  }

  /**
   * Extract important keywords from text
   */
  private extractKeywords(words: string[]): string[] {
    // Simple keyword extraction based on frequency and importance
    const stopWords = new Set(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
    const filteredWords = words.filter(word => !stopWords.has(word) && word.length > 3);

    // Count frequency
    const wordCount = new Map<string, number>();
    filteredWords.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });

    // Return top keywords
    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
  }

  /**
   * Determine overall emotional tone
   */
  private determineEmotionalTone(humor: number, confidence: number, vulnerability: number, polarity: number): string {
    if (humor > 0.6) return 'playful';
    if (confidence > 0.7) return 'assertive';
    if (vulnerability > 0.6) return 'introspective';
    if (polarity > 0.7) return 'optimistic';
    if (polarity < 0.3) return 'melancholic';
    return 'balanced';
  }

  /**
   * Get fallback analysis when API fails
   */
  private getFallbackAnalysis(bioText: string): BioAnalysis {
    return {
      humor: 0.5,
      confidence: 0.5,
      vulnerability: 0.5,
      tonePolarity: 0.5,
      creativity: 0.5,
      keywords: bioText.split(/\W+/).filter(w => w.length > 3).slice(0, 5),
      emotionalTone: 'neutral',
      analysisTimestamp: new Date()
    };
  }

  /**
   * Simple text hashing for cache keys
   */
  private hashText(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  /**
   * Clear analysis cache
   */
  clearCache(): void {
    this.analysisCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.analysisCache.size,
      keys: Array.from(this.analysisCache.keys())
    };
  }
}

// Export singleton instance
export const nlpEngine = NLPEngine.getInstance();
