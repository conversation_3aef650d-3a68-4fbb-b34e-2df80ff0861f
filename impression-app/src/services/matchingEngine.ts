import type {
  BehavioralVector,
  Match,
  MatchType,
  User,
  LocationZone,
  MatchPrediction,
  ConnectionIntent
} from '@/types';
import { calculateCosineSimilarity, calculateDynamicThreshold } from '@/lib/utils';
import { behavioralEngine } from './behavioralEngine';

export class ResonanceMatchingEngine {
  private static instance: ResonanceMatchingEngine;
  private baseThreshold = 0.72;
  private densityCache = new Map<string, number>();
  private matchQueue: Array<{ userA: string; userB: string; timestamp: number }> = [];
  private isProcessingMatches = false;

  static getInstance(): ResonanceMatchingEngine {
    if (!ResonanceMatchingEngine.instance) {
      ResonanceMatchingEngine.instance = new ResonanceMatchingEngine();
    }
    return ResonanceMatchingEngine.instance;
  }

  /**
   * Check for potential match between two users
   */
  async checkMutualMatch(
    userA: User,
    userB: User,
    vectorA: BehavioralVector,
    vectorB: BehavioralVector,
    zone: LocationZone
  ): Promise<Match | null> {
    // Calculate dynamic threshold based on zone density
    const threshold = this.calculateZoneThreshold(zone);

    // Compare behavioral vectors
    const vectorComparison = behavioralEngine.compareVectors(vectorA, vectorB);

    // Check if similarity meets threshold
    if (vectorComparison.similarity < threshold) {
      return null;
    }

    // Determine match type
    const matchType = this.determineMatchType(userA, userB, vectorComparison);

    // Calculate overall match prediction
    const prediction = this.calculateMatchPrediction(userA, userB, vectorComparison);

    // Create match object
    const match: Match = {
      id: `match_${userA.id}_${userB.id}_${Date.now()}`,
      users: [userA.id, userB.id],
      matchType,
      similarity: vectorComparison.similarity,
      confidence: prediction.confidence,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 48 * 60 * 60 * 1000), // 48 hours
      status: 'pending',
      revealLevel: 'anonymous'
    };

    // Add to processing queue
    this.matchQueue.push({
      userA: userA.id,
      userB: userB.id,
      timestamp: Date.now()
    });

    this.processMatchQueue();

    return match;
  }

  /**
   * Calculate dynamic threshold based on zone density
   */
  private calculateZoneThreshold(zone: LocationZone): number {
    const medianDensity = 0.5; // This would be calculated from historical data
    return calculateDynamicThreshold(this.baseThreshold, zone.density, medianDensity);
  }

  /**
   * Determine match type based on behavioral patterns and declared intents
   */
  private determineMatchType(
    userA: User,
    userB: User,
    vectorComparison: { similarity: number; confidence: number; breakdown: Record<string, number> }
  ): MatchType {
    const { breakdown } = vectorComparison;

    // Analyze behavioral signals
    const photoFocusScore = (breakdown.photoFocus || 0);
    const bioFocusScore = (breakdown.bioFocus || 0);
    const engagementScore = (breakdown.engagement || 0);
    const dwellTimeScore = (breakdown.dwellTime || 0);

    // Get declared intents
    const userAIntents = userA.connectionIntents.map(i => i.type);
    const userBIntents = userB.connectionIntents.map(i => i.type);
    const commonIntents = userAIntents.filter(intent => userBIntents.includes(intent));

    // Classification weights
    const behavioralWeight = 0.4;
    const intentWeight = 0.3;
    const compatibilityWeight = 0.3;

    // Calculate scores for each match type
    const scores = {
      'first-flirt': this.calculateMatchTypeScore('romantic', {
        behavioral: (photoFocusScore * 0.4 + dwellTimeScore * 0.3 + engagementScore * 0.3),
        intent: commonIntents.includes('romantic') ? 1 : 0,
        compatibility: vectorComparison.similarity
      }, { behavioralWeight, intentWeight, compatibilityWeight }),

      'first-meet': this.calculateMatchTypeScore('platonic', {
        behavioral: (bioFocusScore * 0.4 + engagementScore * 0.3 + breakdown.interactionDepth * 0.3),
        intent: commonIntents.includes('platonic') ? 1 : 0,
        compatibility: vectorComparison.similarity
      }, { behavioralWeight, intentWeight, compatibilityWeight }),

      'first-collab': this.calculateMatchTypeScore('collaborative', {
        behavioral: (bioFocusScore * 0.3 + breakdown.detailsFocus * 0.4 + engagementScore * 0.3),
        intent: commonIntents.includes('collaborative') ? 1 : 0,
        compatibility: vectorComparison.similarity
      }, { behavioralWeight, intentWeight, compatibilityWeight }),

      'first-sync': this.calculateMatchTypeScore('creative', {
        behavioral: (engagementScore * 0.4 + breakdown.scrollConsistency * 0.3 + bioFocusScore * 0.3),
        intent: commonIntents.includes('creative') ? 1 : 0,
        compatibility: vectorComparison.similarity
      }, { behavioralWeight, intentWeight, compatibilityWeight })
    };

    // Find highest scoring match type
    const bestMatch = Object.entries(scores).reduce((best, [type, score]) =>
      score > best.score ? { type: type as MatchType, score } : best,
      { type: 'first-encounter' as MatchType, score: 0 }
    );

    // Return best match if confidence is high enough, otherwise default to encounter
    return bestMatch.score > 0.6 ? bestMatch.type : 'first-encounter';
  }

  /**
   * Calculate match type score
   */
  private calculateMatchTypeScore(
    type: string,
    factors: { behavioral: number; intent: number; compatibility: number },
    weights: { behavioralWeight: number; intentWeight: number; compatibilityWeight: number }
  ): number {
    return (
      factors.behavioral * weights.behavioralWeight +
      factors.intent * weights.intentWeight +
      factors.compatibility * weights.compatibilityWeight
    );
  }

  /**
   * Calculate comprehensive match prediction
   */
  private calculateMatchPrediction(
    userA: User,
    userB: User,
    vectorComparison: { similarity: number; confidence: number; breakdown: Record<string, number> }
  ): MatchPrediction {
    const behavioral = vectorComparison.similarity;

    // Calculate intentional compatibility
    const intentional = this.calculateIntentionalCompatibility(
      userA.connectionIntents,
      userB.connectionIntents
    );

    // Calculate linguistic compatibility (simplified - would use AI NLP in production)
    const linguistic = this.calculateLinguisticCompatibility(userA.bio, userB.bio);

    // Overall compatibility
    const compatibility = (behavioral * 0.5 + intentional * 0.3 + linguistic * 0.2);

    return {
      compatibility,
      matchType: this.determineMatchType(userA, userB, vectorComparison),
      confidence: vectorComparison.confidence,
      factors: {
        behavioral,
        intentional,
        linguistic
      }
    };
  }

  /**
   * Calculate intentional compatibility
   */
  private calculateIntentionalCompatibility(
    intentsA: ConnectionIntent[],
    intentsB: ConnectionIntent[]
  ): number {
    if (intentsA.length === 0 || intentsB.length === 0) return 0.5;

    const typesA = intentsA.map(i => i.type);
    const typesB = intentsB.map(i => i.type);
    const commonTypes = typesA.filter(type => typesB.includes(type));

    if (commonTypes.length === 0) return 0.2;

    // Calculate weighted compatibility based on intensity
    let totalCompatibility = 0;
    let totalWeight = 0;

    commonTypes.forEach(type => {
      const intentA = intentsA.find(i => i.type === type);
      const intentB = intentsB.find(i => i.type === type);

      if (intentA && intentB) {
        const intensityCompatibility = 1 - Math.abs(intentA.intensity - intentB.intensity);
        const weight = (intentA.intensity + intentB.intensity) / 2;

        totalCompatibility += intensityCompatibility * weight;
        totalWeight += weight;
      }
    });

    return totalWeight > 0 ? totalCompatibility / totalWeight : 0.5;
  }

  /**
   * Calculate linguistic compatibility (simplified)
   */
  private calculateLinguisticCompatibility(bioA?: string, bioB?: string): number {
    if (!bioA || !bioB) return 0.5;

    // Simple word overlap analysis (would be replaced with AI NLP)
    const wordsA = bioA.toLowerCase().split(/\W+/).filter(w => w.length > 3);
    const wordsB = bioB.toLowerCase().split(/\W+/).filter(w => w.length > 3);

    const commonWords = wordsA.filter(word => wordsB.includes(word));
    const totalUniqueWords = new Set([...wordsA, ...wordsB]).size;

    return totalUniqueWords > 0 ? commonWords.length / totalUniqueWords : 0.5;
  }

  /**
   * Process match queue
   */
  private async processMatchQueue(): Promise<void> {
    if (this.isProcessingMatches) return;

    this.isProcessingMatches = true;

    while (this.matchQueue.length > 0) {
      const batch = this.matchQueue.splice(0, 5); // Process in small batches

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 200));

      // In a real implementation, this would:
      // 1. Store matches in database
      // 2. Send notifications to users
      // 3. Update matching statistics
      // 4. Trigger analytics events

      console.log(`Processed ${batch.length} potential matches`);
    }

    this.isProcessingMatches = false;
  }

  /**
   * Update base threshold based on system performance
   */
  updateBaseThreshold(newThreshold: number): void {
    if (newThreshold >= 0.65 && newThreshold <= 0.85) {
      this.baseThreshold = newThreshold;
    }
  }

  /**
   * Get current base threshold
   */
  getBaseThreshold(): number {
    return this.baseThreshold;
  }

  /**
   * Get match queue statistics
   */
  getQueueStats(): { queueLength: number; isProcessing: boolean } {
    return {
      queueLength: this.matchQueue.length,
      isProcessing: this.isProcessingMatches
    };
  }

  /**
   * Clear match queue
   */
  clearQueue(): void {
    this.matchQueue = [];
  }
}

// Export singleton instance
export const matchingEngine = ResonanceMatchingEngine.getInstance();
