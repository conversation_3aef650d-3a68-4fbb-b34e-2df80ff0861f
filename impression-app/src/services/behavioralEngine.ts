import type { BehavioralVector, InteractionData, DeviceCapabilities } from '@/types';
import { calculateCosineSimilarity, calculateDynamicThreshold } from '@/lib/utils';

export class BehavioralVectorEngine {
  private static instance: BehavioralVectorEngine;
  private vectorCache = new Map<string, BehavioralVector>();
  private processingQueue: InteractionData[] = [];
  private isProcessing = false;

  static getInstance(): BehavioralVectorEngine {
    if (!BehavioralVectorEngine.instance) {
      BehavioralVectorEngine.instance = new BehavioralVectorEngine();
    }
    return BehavioralVectorEngine.instance;
  }

  /**
   * Process interaction data into a behavioral vector
   */
  async processInteraction(data: InteractionData, deviceCapabilities: DeviceCapabilities): Promise<BehavioralVector> {
    // Add to processing queue
    this.processingQueue.push(data);

    if (!this.isProcessing) {
      this.isProcessing = true;
      await this.processQueue();
      this.isProcessing = false;
    }

    // Generate vector from interaction data
    return this.generateVector(data, deviceCapabilities);
  }

  /**
   * Generate behavioral vector from interaction data
   */
  private generateVector(data: InteractionData, deviceCapabilities: DeviceCapabilities): BehavioralVector {
    const dimensions = this.calculateDimensions(data, deviceCapabilities);

    const vector: BehavioralVector = {
      dimensions,
      timestamp: data.timestamp,
      deviceTier: deviceCapabilities.tier,
      confidence: this.calculateConfidence(data, deviceCapabilities),
      sessionId: data.sessionId
    };

    // Cache the vector
    this.vectorCache.set(`${data.targetUserId}_${data.sessionId}`, vector);

    return vector;
  }

  /**
   * Calculate 10-dimensional vector from interaction data
   */
  private calculateDimensions(data: InteractionData, capabilities: DeviceCapabilities): number[] {
    // Normalize dwell time (0-1 scale, 30 seconds = 1.0)
    const dwellTimeNorm = Math.min(data.dwellTime / 30000, 1);

    // Normalize scroll velocity
    const avgVelocity = data.scrollBehavior.velocity.reduce((a, b) => a + b, 0) / data.scrollBehavior.velocity.length || 0;
    const scrollVelocityNorm = Math.min(avgVelocity / 1000, 1);

    // Normalize scroll distance
    const scrollDistanceNorm = Math.min(data.scrollBehavior.totalDistance / 5000, 1);

    // Calculate focus distribution
    const totalFocusTime = data.focusAreas.reduce((sum, area) => sum + area.timeSpent, 0);
    const photoFocus = data.focusAreas.filter(a => a.element === 'photo').reduce((sum, a) => sum + a.timeSpent, 0) / totalFocusTime || 0;
    const bioFocus = data.focusAreas.filter(a => a.element === 'bio').reduce((sum, a) => sum + a.timeSpent, 0) / totalFocusTime || 0;
    const detailsFocus = data.focusAreas.filter(a => a.element === 'details').reduce((sum, a) => sum + a.timeSpent, 0) / totalFocusTime || 0;

    // Normalize interaction depth (0-1 scale, 10 interactions = 1.0)
    const interactionDepthNorm = Math.min(data.interactionDepth / 10, 1);

    // Calculate return behavior (0-1 scale, 5 returns = 1.0)
    const returnBehaviorNorm = Math.min(data.returnVisits / 5, 1);

    // Calculate engagement intensity
    const engagementIntensity = (dwellTimeNorm + interactionDepthNorm) / 2;

    // Calculate scroll pattern consistency
    const scrollConsistency = this.calculateScrollConsistency(data.scrollBehavior.velocity);

    return [
      dwellTimeNorm,           // 0: Overall time spent
      scrollVelocityNorm,      // 1: Scroll speed
      scrollDistanceNorm,      // 2: Total scroll distance
      photoFocus,              // 3: Photo attention ratio
      bioFocus,                // 4: Bio attention ratio
      detailsFocus,            // 5: Details attention ratio
      interactionDepthNorm,    // 6: Interaction frequency
      returnBehaviorNorm,      // 7: Return visits
      engagementIntensity,     // 8: Overall engagement
      scrollConsistency        // 9: Scroll pattern consistency
    ];
  }

  /**
   * Calculate scroll pattern consistency
   */
  private calculateScrollConsistency(velocities: number[]): number {
    if (velocities.length < 2) return 0;

    const mean = velocities.reduce((a, b) => a + b, 0) / velocities.length;
    const variance = velocities.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / velocities.length;
    const standardDeviation = Math.sqrt(variance);

    // Lower standard deviation = higher consistency
    return Math.max(0, 1 - (standardDeviation / mean));
  }

  /**
   * Calculate confidence score for the vector
   */
  private calculateConfidence(data: InteractionData, capabilities: DeviceCapabilities): number {
    let confidence = 0.5; // Base confidence

    // Boost confidence based on data quality
    if (data.dwellTime > 5000) confidence += 0.1; // 5+ seconds
    if (data.interactionDepth > 2) confidence += 0.1; // Multiple interactions
    if (data.focusAreas.length > 1) confidence += 0.1; // Multiple focus areas
    if (data.scrollBehavior.totalDistance > 100) confidence += 0.1; // Meaningful scrolling

    // Adjust for device capabilities
    switch (capabilities.tier) {
      case 'advanced':
        confidence += 0.1;
        break;
      case 'standard':
        confidence += 0.05;
        break;
      case 'basic':
        // No adjustment
        break;
    }

    return Math.min(confidence, 1);
  }

  /**
   * Combine multiple vectors (for Unit Profiles)
   */
  combineVectors(vectors: BehavioralVector[], weights?: number[]): BehavioralVector {
    if (vectors.length === 0) {
      throw new Error('Cannot combine empty vector array');
    }

    if (vectors.length === 1) {
      return vectors[0];
    }

    const defaultWeights = vectors.map(() => 1 / vectors.length);
    const actualWeights = weights || defaultWeights;

    // Ensure weights sum to 1
    const weightSum = actualWeights.reduce((a, b) => a + b, 0);
    const normalizedWeights = actualWeights.map(w => w / weightSum);

    // Combine dimensions
    const combinedDimensions = new Array(10).fill(0);
    vectors.forEach((vector, index) => {
      const weight = normalizedWeights[index];
      vector.dimensions.forEach((dim, dimIndex) => {
        combinedDimensions[dimIndex] += dim * weight;
      });
    });

    // Calculate combined confidence
    const avgConfidence = vectors.reduce((sum, v) => sum + v.confidence, 0) / vectors.length;

    return {
      dimensions: combinedDimensions,
      timestamp: Date.now(),
      deviceTier: this.getMostCapableTier(vectors.map(v => v.deviceTier)),
      confidence: avgConfidence,
      sessionId: `combined_${Date.now()}`
    };
  }

  /**
   * Get the most capable device tier from a list
   */
  private getMostCapableTier(tiers: ('basic' | 'standard' | 'advanced')[]): 'basic' | 'standard' | 'advanced' {
    if (tiers.includes('advanced')) return 'advanced';
    if (tiers.includes('standard')) return 'standard';
    return 'basic';
  }

  /**
   * Compare two behavioral vectors
   */
  compareVectors(vectorA: BehavioralVector, vectorB: BehavioralVector): {
    similarity: number;
    confidence: number;
    breakdown: Record<string, number>;
  } {
    const similarity = calculateCosineSimilarity(vectorA.dimensions, vectorB.dimensions);
    const confidence = Math.min(vectorA.confidence, vectorB.confidence);

    // Calculate dimension-wise breakdown
    const dimensionNames = [
      'dwellTime', 'scrollVelocity', 'scrollDistance', 'photoFocus',
      'bioFocus', 'detailsFocus', 'interactionDepth', 'returnBehavior',
      'engagement', 'scrollConsistency'
    ];

    const breakdown: Record<string, number> = {};
    vectorA.dimensions.forEach((dimA, index) => {
      const dimB = vectorB.dimensions[index];
      breakdown[dimensionNames[index]] = 1 - Math.abs(dimA - dimB);
    });

    return { similarity, confidence, breakdown };
  }

  /**
   * Process the interaction queue
   */
  private async processQueue(): Promise<void> {
    while (this.processingQueue.length > 0) {
      const batch = this.processingQueue.splice(0, 10); // Process in batches

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 100));

      // In a real implementation, this would:
      // 1. Store vectors in database
      // 2. Update user profiles
      // 3. Trigger matching algorithms
      // 4. Send analytics events

      console.log(`Processed batch of ${batch.length} interactions`);
    }
  }

  /**
   * Get cached vector
   */
  getCachedVector(userId: string, sessionId: string): BehavioralVector | null {
    return this.vectorCache.get(`${userId}_${sessionId}`) || null;
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.vectorCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.vectorCache.size,
      keys: Array.from(this.vectorCache.keys())
    };
  }
}

// Export singleton instance
export const behavioralEngine = BehavioralVectorEngine.getInstance();
