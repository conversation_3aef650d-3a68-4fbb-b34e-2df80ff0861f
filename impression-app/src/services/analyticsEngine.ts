import type { AnalyticsEvent, User, Match, BehavioralVector, InteractionData } from '@/types';

export class AnalyticsEngine {
  private static instance: AnalyticsEngine;
  private events: AnalyticsEvent[] = [];
  private sessionId: string;
  private userId?: string;
  private isEnabled = true;

  static getInstance(): AnalyticsEngine {
    if (!AnalyticsEngine.instance) {
      AnalyticsEngine.instance = new AnalyticsEngine();
    }
    return AnalyticsEngine.instance;
  }

  constructor() {
    this.sessionId = this.generateSessionId();
  }

  /**
   * Initialize analytics with user context
   */
  initialize(user: User): void {
    this.userId = user.id;
    this.track('session_start', {
      user_id: user.id,
      onboarding_complete: user.onboardingComplete,
      privacy_settings: user.privacySettings,
      connection_intents: user.connectionIntents.map(i => i.type)
    });
  }

  /**
   * Track an analytics event
   */
  track(event: string, properties: Record<string, any> = {}): void {
    if (!this.isEnabled) return;

    const analyticsEvent: AnalyticsEvent = {
      event,
      properties: {
        ...properties,
        session_id: this.sessionId,
        timestamp: Date.now(),
        user_agent: navigator.userAgent,
        url: window.location.href,
        referrer: document.referrer
      },
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: new Date()
    };

    this.events.push(analyticsEvent);

    // In production, send to analytics service
    this.sendToAnalyticsService(analyticsEvent);
  }

  /**
   * Track page view
   */
  trackPageView(page: string, properties: Record<string, any> = {}): void {
    this.track('page_view', {
      page,
      ...properties
    });
  }

  /**
   * Track user interaction
   */
  trackInteraction(element: string, action: string, properties: Record<string, any> = {}): void {
    this.track('user_interaction', {
      element,
      action,
      ...properties
    });
  }

  /**
   * Track behavioral data
   */
  trackBehavioralData(data: InteractionData, vector: BehavioralVector): void {
    this.track('behavioral_data', {
      target_user_id: data.targetUserId,
      dwell_time: data.dwellTime,
      scroll_distance: data.scrollBehavior.totalDistance,
      interaction_depth: data.interactionDepth,
      focus_areas: data.focusAreas.length,
      vector_confidence: vector.confidence,
      device_tier: vector.deviceTier
    });
  }

  /**
   * Track match events
   */
  trackMatch(match: Match, action: 'created' | 'accepted' | 'declined' | 'expired'): void {
    this.track('match_event', {
      match_id: match.id,
      match_type: match.matchType,
      similarity: match.similarity,
      confidence: match.confidence,
      action,
      time_to_action: action !== 'created' ? Date.now() - match.createdAt.getTime() : 0
    });
  }

  /**
   * Track onboarding progress
   */
  trackOnboardingStep(step: string, completed: boolean, properties: Record<string, any> = {}): void {
    this.track('onboarding_step', {
      step,
      completed,
      ...properties
    });
  }

  /**
   * Track privacy settings changes
   */
  trackPrivacyChange(setting: string, oldValue: any, newValue: any): void {
    this.track('privacy_change', {
      setting,
      old_value: oldValue,
      new_value: newValue
    });
  }

  /**
   * Track performance metrics
   */
  trackPerformance(metric: string, value: number, unit: string = 'ms'): void {
    this.track('performance_metric', {
      metric,
      value,
      unit
    });
  }

  /**
   * Track errors
   */
  trackError(error: Error, context: Record<string, any> = {}): void {
    this.track('error', {
      error_message: error.message,
      error_stack: error.stack,
      error_name: error.name,
      ...context
    });
  }

  /**
   * Track conversion events
   */
  trackConversion(event: string, value?: number, properties: Record<string, any> = {}): void {
    this.track('conversion', {
      conversion_event: event,
      value,
      ...properties
    });
  }

  /**
   * Generate analytics report
   */
  generateReport(timeframe: 'day' | 'week' | 'month' = 'day'): {
    totalEvents: number;
    uniqueEvents: number;
    topEvents: Array<{ event: string; count: number }>;
    userJourney: string[];
    performanceMetrics: Record<string, number>;
  } {
    const now = Date.now();
    const timeframeDuration = {
      day: 24 * 60 * 60 * 1000,
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000
    }[timeframe];

    const relevantEvents = this.events.filter(
      event => now - event.timestamp.getTime() <= timeframeDuration
    );

    // Count events
    const eventCounts = new Map<string, number>();
    relevantEvents.forEach(event => {
      eventCounts.set(event.event, (eventCounts.get(event.event) || 0) + 1);
    });

    // Top events
    const topEvents = Array.from(eventCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([event, count]) => ({ event, count }));

    // User journey
    const userJourney = relevantEvents
      .filter(event => ['page_view', 'user_interaction', 'match_event'].includes(event.event))
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
      .map(event => event.event);

    // Performance metrics
    const performanceEvents = relevantEvents.filter(event => event.event === 'performance_metric');
    const performanceMetrics: Record<string, number> = {};
    performanceEvents.forEach(event => {
      const metric = event.properties.metric;
      const value = event.properties.value;
      if (!performanceMetrics[metric]) {
        performanceMetrics[metric] = value;
      } else {
        performanceMetrics[metric] = (performanceMetrics[metric] + value) / 2; // Average
      }
    });

    return {
      totalEvents: relevantEvents.length,
      uniqueEvents: eventCounts.size,
      topEvents,
      userJourney,
      performanceMetrics
    };
  }

  /**
   * Get user behavior insights
   */
  getUserBehaviorInsights(): {
    sessionDuration: number;
    pageViews: number;
    interactions: number;
    mostViewedPages: string[];
    engagementScore: number;
  } {
    const sessionEvents = this.events.filter(event => event.sessionId === this.sessionId);

    if (sessionEvents.length === 0) {
      return {
        sessionDuration: 0,
        pageViews: 0,
        interactions: 0,
        mostViewedPages: [],
        engagementScore: 0
      };
    }

    const firstEvent = sessionEvents[0];
    const lastEvent = sessionEvents[sessionEvents.length - 1];
    const sessionDuration = lastEvent.timestamp.getTime() - firstEvent.timestamp.getTime();

    const pageViews = sessionEvents.filter(event => event.event === 'page_view').length;
    const interactions = sessionEvents.filter(event => event.event === 'user_interaction').length;

    // Most viewed pages
    const pageViewEvents = sessionEvents.filter(event => event.event === 'page_view');
    const pageCounts = new Map<string, number>();
    pageViewEvents.forEach(event => {
      const page = event.properties.page;
      pageCounts.set(page, (pageCounts.get(page) || 0) + 1);
    });
    const mostViewedPages = Array.from(pageCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([page]) => page);

    // Engagement score (0-100)
    const engagementScore = Math.min(100,
      (interactions * 10) +
      (pageViews * 5) +
      (sessionDuration / 1000 / 60) // minutes
    );

    return {
      sessionDuration,
      pageViews,
      interactions,
      mostViewedPages,
      engagementScore
    };
  }

  /**
   * Enable or disable analytics
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    if (!enabled) {
      this.track('analytics_disabled');
    } else {
      this.track('analytics_enabled');
    }
  }

  /**
   * Clear all analytics data
   */
  clearData(): void {
    this.events = [];
    this.track('analytics_data_cleared');
  }

  /**
   * Export analytics data
   */
  exportData(): {
    sessionId: string;
    userId?: string;
    events: AnalyticsEvent[];
    summary: any;
  } {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      events: this.events,
      summary: this.generateReport('month')
    };
  }

  /**
   * Send event to analytics service (mock implementation)
   */
  private sendToAnalyticsService(event: AnalyticsEvent): void {
    // In production, this would send to services like:
    // - Google Analytics
    // - Mixpanel
    // - Amplitude
    // - Custom analytics backend

    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics Event:', event);
    }

    // Mock API call
    // fetch('/api/analytics', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(event)
    // });
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const analytics = AnalyticsEngine.getInstance();
