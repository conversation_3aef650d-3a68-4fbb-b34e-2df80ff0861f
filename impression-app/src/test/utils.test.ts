import { describe, it, expect } from 'vitest'
import { 
  calculateCosineSimilarity, 
  calculateDynamicThreshold,
  normalizeBehavioralVector,
  detectDeviceCapabilities,
  validateEmail,
  validatePassword
} from '@/lib/utils'

describe('Utils', () => {
  describe('calculateCosineSimilarity', () => {
    it('should calculate cosine similarity correctly', () => {
      const vectorA = [1, 0, 1, 0]
      const vectorB = [0, 1, 0, 1]
      const similarity = calculateCosineSimilarity(vectorA, vectorB)
      expect(similarity).toBe(0)
    })

    it('should return 1 for identical vectors', () => {
      const vectorA = [1, 2, 3]
      const vectorB = [1, 2, 3]
      const similarity = calculateCosineSimilarity(vectorA, vectorB)
      expect(similarity).toBe(1)
    })

    it('should handle zero vectors', () => {
      const vectorA = [0, 0, 0]
      const vectorB = [1, 2, 3]
      const similarity = calculateCosineSimilarity(vectorA, vectorB)
      expect(similarity).toBe(0)
    })
  })

  describe('calculateDynamicThreshold', () => {
    it('should adjust threshold based on density', () => {
      const baseThreshold = 0.7
      const localDensity = 0.8
      const medianDensity = 0.5
      
      const threshold = calculateDynamicThreshold(baseThreshold, localDensity, medianDensity)
      expect(threshold).toBeGreaterThan(baseThreshold)
      expect(threshold).toBeLessThanOrEqual(0.85)
    })

    it('should clamp threshold within bounds', () => {
      const baseThreshold = 0.7
      const localDensity = 10
      const medianDensity = 0.1
      
      const threshold = calculateDynamicThreshold(baseThreshold, localDensity, medianDensity)
      expect(threshold).toBe(0.85) // Should be clamped to max
    })
  })

  describe('normalizeBehavioralVector', () => {
    it('should normalize behavioral data correctly', () => {
      const rawData = {
        dwellTime: 15000, // 15 seconds
        scrollVelocity: 500,
        returnCount: 2,
        focusAreas: [0.5, 0.3, 0.2],
        interactionDepth: 5
      }

      const vector = normalizeBehavioralVector(rawData)
      
      expect(vector.dimensions).toHaveLength(10)
      expect(vector.dimensions[0]).toBe(0.5) // dwellTime normalized
      expect(vector.confidence).toBeGreaterThan(0.5)
      expect(vector.timestamp).toBeDefined()
    })
  })

  describe('detectDeviceCapabilities', () => {
    it('should detect device capabilities', () => {
      const capabilities = detectDeviceCapabilities()
      
      expect(capabilities).toHaveProperty('tier')
      expect(capabilities).toHaveProperty('hasCamera')
      expect(capabilities).toHaveProperty('hasMotionSensors')
      expect(capabilities).toHaveProperty('hasHighPrecisionTimer')
      expect(capabilities).toHaveProperty('batteryOptimized')
    })
  })

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true)
      expect(validateEmail('<EMAIL>')).toBe(true)
    })

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false)
      expect(validateEmail('test@')).toBe(false)
      expect(validateEmail('@domain.com')).toBe(false)
    })
  })

  describe('validatePassword', () => {
    it('should validate strong passwords', () => {
      const result = validatePassword('StrongPass123!')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject weak passwords', () => {
      const result = validatePassword('weak')
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })
})
