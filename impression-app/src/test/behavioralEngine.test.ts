import { describe, it, expect, beforeEach } from 'vitest'
import { BehavioralVectorEngine } from '@/services/behavioralEngine'
import { InteractionData, DeviceCapabilities } from '@/types'

describe('BehavioralVectorEngine', () => {
  let engine: BehavioralVectorEngine
  let mockInteractionData: InteractionData
  let mockDeviceCapabilities: DeviceCapabilities

  beforeEach(() => {
    engine = BehavioralVectorEngine.getInstance()
    engine.clearCache()

    mockInteractionData = {
      targetUserId: 'user123',
      dwellTime: 15000,
      scrollBehavior: {
        totalDistance: 1000,
        velocity: [100, 150, 200, 100],
        direction: 'down',
        pausePoints: [100, 500, 800]
      },
      focusAreas: [
        { element: 'photo', timeSpent: 5000, intensity: 3 },
        { element: 'bio', timeSpent: 8000, intensity: 2 },
        { element: 'details', timeSpent: 2000, intensity: 1 }
      ],
      returnVisits: 1,
      interactionDepth: 5,
      timestamp: Date.now(),
      sessionId: 'session123'
    }

    mockDeviceCapabilities = {
      tier: 'advanced',
      hasCamera: true,
      hasMotionSensors: true,
      hasHighPrecisionTimer: true,
      batteryOptimized: false,
      supportedFeatures: ['camera', 'motion', 'timer']
    }
  })

  describe('processInteraction', () => {
    it('should process interaction data and return behavioral vector', async () => {
      const vector = await engine.processInteraction(mockInteractionData, mockDeviceCapabilities)
      
      expect(vector).toBeDefined()
      expect(vector.dimensions).toHaveLength(10)
      expect(vector.confidence).toBeGreaterThan(0)
      expect(vector.deviceTier).toBe('advanced')
      expect(vector.sessionId).toBe('session123')
    })

    it('should cache processed vectors', async () => {
      await engine.processInteraction(mockInteractionData, mockDeviceCapabilities)
      
      const cached = engine.getCachedVector('user123', 'session123')
      expect(cached).toBeDefined()
      expect(cached?.dimensions).toHaveLength(10)
    })
  })

  describe('combineVectors', () => {
    it('should combine multiple vectors correctly', async () => {
      const vector1 = await engine.processInteraction(mockInteractionData, mockDeviceCapabilities)
      
      const mockInteractionData2 = {
        ...mockInteractionData,
        targetUserId: 'user456',
        dwellTime: 20000,
        sessionId: 'session456'
      }
      const vector2 = await engine.processInteraction(mockInteractionData2, mockDeviceCapabilities)
      
      const combined = engine.combineVectors([vector1, vector2])
      
      expect(combined.dimensions).toHaveLength(10)
      expect(combined.deviceTier).toBe('advanced')
      expect(combined.confidence).toBeGreaterThan(0)
    })

    it('should handle single vector', async () => {
      const vector = await engine.processInteraction(mockInteractionData, mockDeviceCapabilities)
      const combined = engine.combineVectors([vector])
      
      expect(combined).toEqual(vector)
    })

    it('should apply weights correctly', async () => {
      const vector1 = await engine.processInteraction(mockInteractionData, mockDeviceCapabilities)
      const vector2 = await engine.processInteraction({
        ...mockInteractionData,
        sessionId: 'session456'
      }, mockDeviceCapabilities)
      
      const weights = [0.7, 0.3]
      const combined = engine.combineVectors([vector1, vector2], weights)
      
      expect(combined.dimensions).toHaveLength(10)
      // The combined vector should be closer to vector1 due to higher weight
    })
  })

  describe('compareVectors', () => {
    it('should compare vectors and return similarity metrics', async () => {
      const vector1 = await engine.processInteraction(mockInteractionData, mockDeviceCapabilities)
      const vector2 = await engine.processInteraction({
        ...mockInteractionData,
        sessionId: 'session456'
      }, mockDeviceCapabilities)
      
      const comparison = engine.compareVectors(vector1, vector2)
      
      expect(comparison.similarity).toBeGreaterThanOrEqual(0)
      expect(comparison.similarity).toBeLessThanOrEqual(1)
      expect(comparison.confidence).toBeGreaterThan(0)
      expect(comparison.breakdown).toBeDefined()
      expect(Object.keys(comparison.breakdown)).toHaveLength(10)
    })
  })

  describe('cache management', () => {
    it('should manage cache correctly', async () => {
      await engine.processInteraction(mockInteractionData, mockDeviceCapabilities)
      
      let stats = engine.getCacheStats()
      expect(stats.size).toBe(1)
      
      engine.clearCache()
      stats = engine.getCacheStats()
      expect(stats.size).toBe(0)
    })
  })
})
