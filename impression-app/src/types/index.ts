// Core user interface for Impression
export interface User {
  id: string;
  email: string;
  name: string;
  age?: number;
  photos: string[];
  bio?: string;
  location?: {
    zone: string;
    coordinates?: { lat: number; lng: number };
  };
  connectionIntents: ConnectionIntent[];
  voiceTone?: VoiceToneProfile;
  behavioralVector?: BehavioralVector;
  onboardingComplete: boolean;
  privacySettings: PrivacySettings;
  createdAt: Date;
  lastActive: Date;
}

// Connection intent types
export interface ConnectionIntent {
  type: 'romantic' | 'platonic' | 'collaborative' | 'creative' | 'undefined';
  intensity: number; // 0-1 scale
  description?: string;
}

// Voice tone analysis
export interface VoiceToneProfile {
  humor: number;
  confidence: number;
  vulnerability: number;
  creativity: number;
  warmth: number;
  energy: number;
  audioSample?: string; // base64 encoded audio
  analysisTimestamp: Date;
}

// Behavioral tracking
export interface BehavioralVector {
  dimensions: number[]; // 10-dimensional vector
  timestamp: number;
  deviceTier: 'basic' | 'standard' | 'advanced';
  confidence: number;
  sessionId: string;
}

export interface InteractionData {
  targetUserId: string;
  dwellTime: number;
  scrollBehavior: ScrollBehavior;
  focusAreas: FocusArea[];
  returnVisits: number;
  interactionDepth: number;
  timestamp: number;
  sessionId: string;
}

export interface ScrollBehavior {
  totalDistance: number;
  velocity: number[];
  direction: 'up' | 'down' | 'mixed';
  pausePoints: number[];
}

export interface FocusArea {
  element: 'photo' | 'bio' | 'details';
  timeSpent: number;
  coordinates?: { x: number; y: number };
  intensity: number;
}

// Privacy settings
export interface PrivacySettings {
  locationPrecision: 'zone' | 'city' | 'region';
  behavioralTracking: boolean;
  voiceAnalysis: boolean;
  photoAnalysis: boolean;
  dataRetention: number; // days
  anonymousMode: boolean;
}

// Matching system
export interface Match {
  id: string;
  users: [string, string]; // user IDs
  matchType: MatchType;
  similarity: number;
  confidence: number;
  createdAt: Date;
  expiresAt: Date;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  revealLevel: 'anonymous' | 'partial' | 'full';
}

export type MatchType = 
  | 'first-flirt'    // Romantic/Sexual
  | 'first-meet'     // Platonic
  | 'first-collab'   // Collaborative/Creative
  | 'first-encounter' // Undefined/fuzzy
  | 'first-sync';    // Esoteric/abstract

// Dynamic relationships
export interface DynamicRelationship {
  id: string;
  participants: string[]; // user IDs
  name?: string;
  type: RelationshipType;
  isUnitProfile: boolean;
  unitProfile?: UnitProfile;
  createdAt: Date;
  lastInteraction: Date;
  status: 'forming' | 'active' | 'dormant' | 'dissolved';
}

export type RelationshipType = 
  | 'romantic'
  | 'platonic'
  | 'collaborative'
  | 'creative'
  | 'undefined'
  | 'unit';

export interface UnitProfile {
  id: string;
  name: string;
  photos: string[];
  bio?: string;
  combinedVector: BehavioralVector;
  participants: string[];
  createdAt: Date;
}

// Geographic and location
export interface LocationZone {
  id: string;
  name: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  center: {
    lat: number;
    lng: number;
  };
  userCount: number;
  density: number;
}

export interface BubbleGridUser {
  id: string;
  anonymousId: string;
  position: { x: number; y: number };
  size: number;
  color: string;
  activity: number;
  compatibility?: number;
  isUnitProfile: boolean;
}

// Device capabilities
export interface DeviceCapabilities {
  tier: 'basic' | 'standard' | 'advanced';
  hasCamera: boolean;
  hasMotionSensors: boolean;
  hasHighPrecisionTimer: boolean;
  batteryOptimized: boolean;
  supportedFeatures: string[];
}

// AI and NLP
export interface BioAnalysis {
  humor: number;
  confidence: number;
  vulnerability: number;
  tonePolarity: number;
  creativity: number;
  keywords: string[];
  emotionalTone: string;
  analysisTimestamp: Date;
}

export interface MatchPrediction {
  compatibility: number;
  matchType: MatchType;
  confidence: number;
  factors: {
    behavioral: number;
    intentional: number;
    linguistic: number;
  };
}

// Onboarding
export interface OnboardingData {
  personalDetails: {
    name: string;
    age: number;
    pronouns?: string;
    location: string;
  };
  photos: string[];
  bio: string;
  connectionIntents: ConnectionIntent[];
  voiceSample?: string;
  privacyConsent: {
    behavioralTracking: boolean;
    locationSharing: boolean;
    voiceAnalysis: boolean;
    dataProcessing: boolean;
  };
}

// API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// Events and notifications
export interface AppEvent {
  type: string;
  data: any;
  timestamp: Date;
  userId?: string;
}

export interface Notification {
  id: string;
  type: 'match' | 'message' | 'system' | 'reminder';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: Date;
  expiresAt?: Date;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// Analytics
export interface AnalyticsEvent {
  event: string;
  properties: Record<string, any>;
  userId?: string;
  sessionId: string;
  timestamp: Date;
}

// Component props
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
}

export interface FormFieldProps {
  label: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
}
