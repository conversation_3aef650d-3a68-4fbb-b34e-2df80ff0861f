import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Heart, Users, Sparkles, Shield, ArrowRight, Eye } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const LandingPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Track mouse for subtle parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Redirect authenticated users
  useEffect(() => {
    if (isAuthenticated && user) {
      if (!user.onboardingComplete) {
        navigate('/onboarding');
      } else {
        navigate('/map');
      }
    }
  }, [isAuthenticated, user, navigate]);

  const features = [
    {
      icon: Heart,
      title: "Resonance-Based Matching",
      description: "Relationships emerge through behavioral patterns, not declarations. No forced categories—just authentic connections."
    },
    {
      icon: Eye,
      title: "Behavioral Intelligence",
      description: "Advanced tracking of how you interact with profiles reveals subconscious preferences and compatibility."
    },
    {
      icon: Users,
      title: "Dynamic Relationships",
      description: "Form Unit Profiles with others and explore connections as individuals or pairs—relationships evolve naturally."
    },
    {
      icon: Shield,
      title: "Privacy-First Design",
      description: "Location abstraction, anonymous interactions until mutual interest, and complete control over your data."
    }
  ];

  return (
    <div className="min-h-screen bg-black text-white overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-pink-500/10 via-purple-500/10 to-indigo-500/10" />
        
        {/* Floating particles */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
            x: mousePosition.x * 0.1,
            y: mousePosition.y * 0.1,
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.6, 0.3, 0.6],
            x: -mousePosition.x * 0.05,
            y: -mousePosition.y * 0.05,
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 w-32 h-32 bg-indigo-500/30 rounded-full blur-2xl"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.4, 0.8, 0.4],
            x: mousePosition.x * 0.02,
            y: mousePosition.y * 0.02,
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10">
        {/* Header */}
        <header className="p-6 flex justify-between items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-2xl font-bold impression-text-gradient">
              Impression
            </h1>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-x-4"
          >
            <Link to="/login">
              <Button variant="ghost" className="text-gray-300 hover:text-white">
                Sign In
              </Button>
            </Link>
            <Link to="/register">
              <Button className="impression-button">
                Get Started
              </Button>
            </Link>
          </motion.div>
        </header>

        {/* Hero Section */}
        <section className="px-6 py-20 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-5xl md:text-7xl font-bold mb-6">
              Where relationships{' '}
              <span className="impression-text-gradient">emerge</span>{' '}
              through resonance
            </h2>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
              This is not a dating app. It's a resonance-based dynamic system that facilitates 
              emergent relationships through behavior, not declarations.
            </p>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="space-x-4"
            >
              <Link to="/register">
                <Button size="lg" className="impression-button text-lg px-8 py-4">
                  Begin Your Journey
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </motion.div>
          </motion.div>
        </section>

        {/* Features Section */}
        <section className="px-6 py-20">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h3 className="text-3xl md:text-4xl font-bold mb-4">
                A New Paradigm for Connection
              </h3>
              <p className="text-gray-400 text-lg max-w-2xl mx-auto">
                Users are never forced into categories—they are revealed through pattern and reflection.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="impression-card h-full">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <feature.icon className="h-8 w-8 text-pink-400" />
                        </div>
                        <div>
                          <h4 className="text-xl font-semibold mb-2 text-white">
                            {feature.title}
                          </h4>
                          <p className="text-gray-300">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="px-6 py-20">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto text-center"
          >
            <Card className="impression-card">
              <CardContent className="p-12">
                <Sparkles className="h-12 w-12 text-pink-400 mx-auto mb-6" />
                <h3 className="text-3xl font-bold mb-4">
                  Ready to discover authentic connections?
                </h3>
                <p className="text-gray-300 text-lg mb-8">
                  Join Impression and experience relationships that emerge naturally 
                  through behavioral resonance and genuine compatibility.
                </p>
                <Link to="/register">
                  <Button size="lg" className="impression-button text-lg px-8 py-4">
                    Start Your Impression
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </motion.div>
        </section>

        {/* Footer */}
        <footer className="px-6 py-8 border-t border-gray-800">
          <div className="max-w-6xl mx-auto text-center text-gray-400">
            <p>&copy; 2024 Impression. Where relationships emerge through resonance.</p>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default LandingPage;
