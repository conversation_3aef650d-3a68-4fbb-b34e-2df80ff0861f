import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ArrowLeft,
  Settings as SettingsIcon,
  Shield,
  Eye,
  MapPin,
  Mic,
  Camera,
  Clock,
  User,
  Bell,
  Trash2,
  Download,
  LogOut,
  Edit,
  Save,
  X,
  Check,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import type { PrivacySettings } from '@/types';

const Settings: React.FC = () => {
  const { user, updateUser, updatePrivacySettings, logout } = useAuth();
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState<'profile' | 'privacy' | 'account'>('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [editedProfile, setEditedProfile] = useState({
    name: user?.name || '',
    bio: user?.bio || '',
    age: user?.age || 0
  });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleSaveProfile = async () => {
    try {
      await updateUser(editedProfile);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  const handlePrivacyToggle = async (setting: keyof PrivacySettings, value: boolean | string | number) => {
    try {
      await updatePrivacySettings({ [setting]: value });
    } catch (error) {
      console.error('Error updating privacy settings:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const ProfileSection: React.FC = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-white">Profile Information</h2>
        {!isEditing ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsEditing(true)}
            className="text-pink-400 hover:text-pink-300"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setIsEditing(false);
                setEditedProfile({
                  name: user?.name || '',
                  bio: user?.bio || '',
                  age: user?.age || 0
                });
              }}
              className="text-gray-400 hover:text-white"
            >
              <X className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSaveProfile}
              className="text-green-400 hover:text-green-300"
            >
              <Save className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <Card className="impression-card">
        <CardContent className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-white mb-2">Name</label>
            {isEditing ? (
              <Input
                value={editedProfile.name}
                onChange={(e) => setEditedProfile(prev => ({ ...prev, name: e.target.value }))}
                className="bg-gray-800 border-gray-600 text-white"
              />
            ) : (
              <p className="text-gray-300">{user?.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">Age</label>
            {isEditing ? (
              <Input
                type="number"
                value={editedProfile.age}
                onChange={(e) => setEditedProfile(prev => ({ ...prev, age: parseInt(e.target.value) }))}
                className="bg-gray-800 border-gray-600 text-white"
              />
            ) : (
              <p className="text-gray-300">{user?.age}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">Bio</label>
            {isEditing ? (
              <textarea
                value={editedProfile.bio}
                onChange={(e) => setEditedProfile(prev => ({ ...prev, bio: e.target.value }))}
                className="w-full h-24 bg-gray-800 border border-gray-600 rounded-lg p-3 text-white placeholder-gray-400 resize-none"
                placeholder="Tell us about yourself..."
              />
            ) : (
              <p className="text-gray-300">{user?.bio || 'No bio added yet'}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">Photos</label>
            <div className="grid grid-cols-3 gap-2">
              {user?.photos?.map((photo, index) => (
                <div key={index} className="aspect-square bg-gray-800 rounded-lg overflow-hidden">
                  <img src={photo} alt={`Photo ${index + 1}`} className="w-full h-full object-cover" />
                </div>
              ))}
              {Array.from({ length: Math.max(0, 6 - (user?.photos?.length || 0)) }).map((_, index) => (
                <div key={index} className="aspect-square bg-gray-800 border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center">
                  <Camera className="h-6 w-6 text-gray-500" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const PrivacySection: React.FC = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-white">Privacy & Data</h2>

      {/* Behavioral Tracking */}
      <Card className="impression-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Eye className="h-5 w-5 text-pink-400" />
            <span>Behavioral Tracking</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">Enable Behavioral Analysis</h4>
              <p className="text-sm text-gray-400">Track interactions to improve matching quality</p>
            </div>
            <button
              onClick={() => handlePrivacyToggle('behavioralTracking', !user?.privacySettings.behavioralTracking)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                user?.privacySettings.behavioralTracking ? 'bg-pink-500' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  user?.privacySettings.behavioralTracking ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">Photo Analysis</h4>
              <p className="text-sm text-gray-400">Analyze photos for better matching</p>
            </div>
            <button
              onClick={() => handlePrivacyToggle('photoAnalysis', !user?.privacySettings.photoAnalysis)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                user?.privacySettings.photoAnalysis ? 'bg-pink-500' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  user?.privacySettings.photoAnalysis ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Location Privacy */}
      <Card className="impression-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <MapPin className="h-5 w-5 text-pink-400" />
            <span>Location Privacy</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium text-white mb-2">Location Precision</h4>
            <div className="space-y-2">
              {[
                { value: 'zone', label: 'Zone Only', description: 'General area (recommended)' },
                { value: 'city', label: 'City Level', description: 'City-wide matching' },
                { value: 'region', label: 'Regional', description: 'Broader regional matching' }
              ].map((option) => (
                <label key={option.value} className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="locationPrecision"
                    value={option.value}
                    checked={user?.privacySettings.locationPrecision === option.value}
                    onChange={(e) => handlePrivacyToggle('locationPrecision', e.target.value)}
                    className="text-pink-500 focus:ring-pink-500"
                  />
                  <div>
                    <div className="text-white font-medium">{option.label}</div>
                    <div className="text-sm text-gray-400">{option.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Voice Analysis */}
      <Card className="impression-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Mic className="h-5 w-5 text-pink-400" />
            <span>Voice Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">Enable Voice Tone Analysis</h4>
              <p className="text-sm text-gray-400">Analyze voice samples for compatibility</p>
            </div>
            <button
              onClick={() => handlePrivacyToggle('voiceAnalysis', !user?.privacySettings.voiceAnalysis)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                user?.privacySettings.voiceAnalysis ? 'bg-pink-500' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  user?.privacySettings.voiceAnalysis ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Data Retention */}
      <Card className="impression-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Clock className="h-5 w-5 text-pink-400" />
            <span>Data Retention</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <h4 className="font-medium text-white mb-2">Data Retention Period</h4>
            <select
              value={user?.privacySettings.dataRetention}
              onChange={(e) => handlePrivacyToggle('dataRetention', parseInt(e.target.value))}
              className="w-full bg-gray-800 border border-gray-600 rounded-lg p-2 text-white"
            >
              <option value={7}>7 days</option>
              <option value={30}>30 days (recommended)</option>
              <option value={90}>90 days</option>
              <option value={365}>1 year</option>
            </select>
            <p className="text-sm text-gray-400 mt-2">
              How long to keep behavioral data for matching improvements
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Anonymous Mode */}
      <Card className="impression-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Shield className="h-5 w-5 text-pink-400" />
            <span>Anonymous Mode</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">Enhanced Privacy Mode</h4>
              <p className="text-sm text-gray-400">Extra privacy protections (may reduce match quality)</p>
            </div>
            <button
              onClick={() => handlePrivacyToggle('anonymousMode', !user?.privacySettings.anonymousMode)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                user?.privacySettings.anonymousMode ? 'bg-pink-500' : 'bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  user?.privacySettings.anonymousMode ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const AccountSection: React.FC = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-white">Account Management</h2>

      {/* Notifications */}
      <Card className="impression-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Bell className="h-5 w-5 text-pink-400" />
            <span>Notifications</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">Match Notifications</h4>
              <p className="text-sm text-gray-400">Get notified about new matches</p>
            </div>
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-pink-500">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">Message Notifications</h4>
              <p className="text-sm text-gray-400">Get notified about new messages</p>
            </div>
            <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-pink-500">
              <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Data Export */}
      <Card className="impression-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Download className="h-5 w-5 text-pink-400" />
            <span>Data Export</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-300 mb-4">
            Download a copy of all your data including profile information, matches, and behavioral patterns.
          </p>
          <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-800">
            <Download className="h-4 w-4 mr-2" />
            Request Data Export
          </Button>
        </CardContent>
      </Card>

      {/* Account Actions */}
      <Card className="impression-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <User className="h-5 w-5 text-pink-400" />
            <span>Account Actions</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={handleLogout}
            variant="outline"
            className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Sign Out
          </Button>

          <Button
            onClick={() => setShowDeleteConfirm(true)}
            variant="outline"
            className="w-full border-red-600 text-red-400 hover:bg-red-900/20"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Account
          </Button>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="p-4 border-b border-gray-800">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate('/map')}
            className="text-gray-400 hover:text-white"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold impression-text-gradient">Settings</h1>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-800">
        <div className="flex">
          {[
            { id: 'profile', label: 'Profile', icon: User },
            { id: 'privacy', label: 'Privacy', icon: Shield },
            { id: 'account', label: 'Account', icon: SettingsIcon }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveSection(tab.id as any)}
                className={`flex-1 py-4 px-6 flex items-center justify-center space-x-2 font-medium transition-colors ${
                  activeSection === tab.id
                    ? 'text-pink-400 border-b-2 border-pink-400'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {activeSection === 'profile' && <ProfileSection />}
        {activeSection === 'privacy' && <PrivacySection />}
        {activeSection === 'account' && <AccountSection />}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="max-w-md w-full"
          >
            <Card className="impression-card">
              <CardContent className="p-6 text-center">
                <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Delete Account</h3>
                <p className="text-gray-300 mb-6">
                  This action cannot be undone. All your data, matches, and connections will be permanently deleted.
                </p>
                <div className="flex space-x-3">
                  <Button
                    onClick={() => setShowDeleteConfirm(false)}
                    variant="outline"
                    className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      // Handle account deletion
                      setShowDeleteConfirm(false);
                    }}
                    className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                  >
                    Delete Forever
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
};

export default Settings;
