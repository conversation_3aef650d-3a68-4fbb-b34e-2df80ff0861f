import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  ArrowLeft,
  Heart,
  X,
  MoreHorizontal,
  MapPin,
  Calendar,
  Briefcase,
  GraduationCap,
  Music,
  Camera,
  Book,
  Coffee,
  Palette,
  Users
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import type { User, InteractionData, BehavioralVector } from '@/types';
import { detectDeviceCapabilities, normalizeBehavioralVector } from '@/lib/utils';

const ProfileView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user: currentUser } = useAuth();
  const navigate = useNavigate();

  // Behavioral tracking state
  const [startTime] = useState(Date.now());
  const [dwellTime, setDwellTime] = useState(0);
  const [scrollBehavior, setScrollBehavior] = useState({
    totalDistance: 0,
    velocity: [] as number[],
    direction: 'mixed' as 'up' | 'down' | 'mixed',
    pausePoints: [] as number[]
  });
  const [focusAreas, setFocusAreas] = useState<Array<{
    element: 'photo' | 'bio' | 'details';
    timeSpent: number;
    coordinates?: { x: number; y: number };
    intensity: number;
  }>>([]);
  const [returnVisits, setReturnVisits] = useState(0);
  const [interactionDepth, setInteractionDepth] = useState(0);

  // Profile state
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [showFullBio, setShowFullBio] = useState(false);
  const [deviceCapabilities] = useState(detectDeviceCapabilities());

  // Refs for tracking
  const profileRef = useRef<HTMLDivElement>(null);
  const photoRef = useRef<HTMLDivElement>(null);
  const bioRef = useRef<HTMLDivElement>(null);
  const detailsRef = useRef<HTMLDivElement>(null);
  const lastScrollY = useRef(0);
  const lastScrollTime = useRef(Date.now());
  const focusStartTimes = useRef<Record<string, number>>({});
  const sessionId = useRef(Math.random().toString(36).substr(2, 9));

  // Mock profile data
  const profile: User = {
    id: id || 'mock_user',
    email: '<EMAIL>',
    name: 'Alex Rivera',
    age: 28,
    photos: [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=600&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=600&fit=crop&crop=face'
    ],
    bio: "I'm passionate about creating meaningful connections through shared experiences. Whether it's exploring hidden coffee shops, discussing the latest book I've read, or collaborating on creative projects, I believe the best relationships emerge naturally through genuine interaction. I work in UX design and love how technology can bring people together in authentic ways.",
    location: {
      zone: 'downtown_sf',
      coordinates: { lat: 37.775, lng: -122.425 }
    },
    connectionIntents: [
      { type: 'romantic', intensity: 0.7, description: 'Looking for genuine romantic connection' },
      { type: 'creative', intensity: 0.8, description: 'Love collaborating on creative projects' }
    ],
    onboardingComplete: true,
    privacySettings: {
      locationPrecision: 'zone',
      behavioralTracking: true,
      voiceAnalysis: false,
      photoAnalysis: true,
      dataRetention: 30,
      anonymousMode: false
    },
    createdAt: new Date('2024-01-15'),
    lastActive: new Date()
  };

  // Behavioral tracking functions
  const trackDwellTime = useCallback(() => {
    const interval = setInterval(() => {
      setDwellTime(Date.now() - startTime);
    }, deviceCapabilities.hasHighPrecisionTimer ? 100 : 1000);

    return () => clearInterval(interval);
  }, [startTime, deviceCapabilities.hasHighPrecisionTimer]);

  const trackScrollBehavior = useCallback((event: Event) => {
    const currentTime = Date.now();
    const currentScrollY = window.scrollY;
    const distance = Math.abs(currentScrollY - lastScrollY.current);
    const timeDiff = currentTime - lastScrollTime.current;
    const velocity = timeDiff > 0 ? distance / timeDiff : 0;

    setScrollBehavior(prev => ({
      totalDistance: prev.totalDistance + distance,
      velocity: [...prev.velocity.slice(-9), velocity], // Keep last 10 values
      direction: currentScrollY > lastScrollY.current ? 'down' :
                currentScrollY < lastScrollY.current ? 'up' : prev.direction,
      pausePoints: velocity < 0.1 ? [...prev.pausePoints, currentScrollY] : prev.pausePoints
    }));

    lastScrollY.current = currentScrollY;
    lastScrollTime.current = currentTime;
  }, []);

  const trackElementFocus = useCallback((element: string, isEntering: boolean, coordinates?: { x: number; y: number }) => {
    if (isEntering) {
      focusStartTimes.current[element] = Date.now();
    } else {
      const startTime = focusStartTimes.current[element];
      if (startTime) {
        const timeSpent = Date.now() - startTime;
        setFocusAreas(prev => {
          const existing = prev.find(area => area.element === element);
          if (existing) {
            return prev.map(area =>
              area.element === element
                ? { ...area, timeSpent: area.timeSpent + timeSpent, intensity: area.intensity + 1 }
                : area
            );
          } else {
            return [...prev, {
              element: element as 'photo' | 'bio' | 'details',
              timeSpent,
              coordinates,
              intensity: 1
            }];
          }
        });
        delete focusStartTimes.current[element];
      }
    }
  }, []);

  // Intersection Observer for element tracking
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const element = entry.target.getAttribute('data-track');
          if (element) {
            trackElementFocus(element, entry.isIntersecting);
          }
        });
      },
      { threshold: 0.5 }
    );

    [photoRef, bioRef, detailsRef].forEach(ref => {
      if (ref.current) {
        observer.observe(ref.current);
      }
    });

    return () => observer.disconnect();
  }, [trackElementFocus]);

  // Set up tracking
  useEffect(() => {
    const cleanupDwellTime = trackDwellTime();

    window.addEventListener('scroll', trackScrollBehavior, { passive: true });

    return () => {
      cleanupDwellTime();
      window.removeEventListener('scroll', trackScrollBehavior);
    };
  }, [trackDwellTime, trackScrollBehavior]);

  // Track interactions
  const handleInteraction = (type: string) => {
    setInteractionDepth(prev => prev + 1);

    // Track specific interactions
    if (type === 'photo_change') {
      trackElementFocus('photo', false);
      trackElementFocus('photo', true);
    } else if (type === 'bio_expand') {
      trackElementFocus('bio', false);
      trackElementFocus('bio', true);
    }
  };

  // Generate behavioral vector on unmount
  useEffect(() => {
    return () => {
      const interactionData: InteractionData = {
        targetUserId: profile.id,
        dwellTime,
        scrollBehavior,
        focusAreas,
        returnVisits,
        interactionDepth,
        timestamp: Date.now(),
        sessionId: sessionId.current
      };

      const behavioralVector = normalizeBehavioralVector({
        dwellTime,
        scrollVelocity: scrollBehavior.velocity.reduce((a, b) => a + b, 0) / scrollBehavior.velocity.length || 0,
        returnCount: returnVisits,
        focusAreas: focusAreas.map(area => area.timeSpent / 1000), // Convert to seconds
        interactionDepth
      });

      // In a real app, this would be sent to the backend
      console.log('Behavioral Data:', { interactionData, behavioralVector });
    };
  }, [dwellTime, scrollBehavior, focusAreas, returnVisits, interactionDepth, profile.id]);

  const handlePhotoChange = (direction: 'next' | 'prev') => {
    handleInteraction('photo_change');
    if (direction === 'next') {
      setCurrentPhotoIndex((prev) => (prev + 1) % profile.photos.length);
    } else {
      setCurrentPhotoIndex((prev) => (prev - 1 + profile.photos.length) % profile.photos.length);
    }
  };

  const handleBioToggle = () => {
    handleInteraction('bio_expand');
    setShowFullBio(!showFullBio);
  };

  const handleAction = (action: 'like' | 'pass') => {
    handleInteraction(`action_${action}`);
    // In a real app, this would trigger the matching logic
    navigate('/map');
  };

  return (
    <div ref={profileRef} className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 p-4 bg-black/80 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate('/map')}
            className="text-white hover:bg-gray-800"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-gray-800"
            >
              <MoreHorizontal className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </header>

      {/* Photo Section */}
      <div
        ref={photoRef}
        data-track="photo"
        className="relative h-screen"
      >
        <AnimatePresence mode="wait">
          <motion.img
            key={currentPhotoIndex}
            src={profile.photos[currentPhotoIndex]}
            alt={`${profile.name} photo ${currentPhotoIndex + 1}`}
            className="w-full h-full object-cover"
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          />
        </AnimatePresence>

        {/* Photo Navigation */}
        {profile.photos.length > 1 && (
          <>
            <button
              onClick={() => handlePhotoChange('prev')}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors"
            >
              ←
            </button>
            <button
              onClick={() => handlePhotoChange('next')}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors"
            >
              →
            </button>
          </>
        )}

        {/* Photo Indicators */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {profile.photos.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentPhotoIndex ? 'bg-white' : 'bg-white/50'
              }`}
            />
          ))}
        </div>

        {/* Gradient Overlay */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/80 to-transparent" />

        {/* Basic Info Overlay */}
        <div className="absolute bottom-4 left-4 right-4">
          <h1 className="text-3xl font-bold text-white mb-2">{profile.name}</h1>
          <div className="flex items-center space-x-4 text-gray-300">
            <span>{profile.age}</span>
            <div className="flex items-center space-x-1">
              <MapPin className="h-4 w-4" />
              <span>Downtown SF</span>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="bg-black px-4 pb-24">
        {/* Bio Section */}
        <div
          ref={bioRef}
          data-track="bio"
          className="py-6 border-b border-gray-800"
        >
          <h2 className="text-xl font-semibold text-white mb-3">About</h2>
          <div className="relative">
            <p className={`text-gray-300 leading-relaxed ${
              showFullBio ? '' : 'line-clamp-3'
            }`}>
              {profile.bio}
            </p>
            {profile.bio && profile.bio.length > 150 && (
              <button
                onClick={handleBioToggle}
                className="text-pink-400 hover:text-pink-300 mt-2 text-sm font-medium"
              >
                {showFullBio ? 'Show less' : 'Read more'}
              </button>
            )}
          </div>
        </div>

        {/* Connection Intents */}
        <div className="py-6 border-b border-gray-800">
          <h2 className="text-xl font-semibold text-white mb-3">Looking for</h2>
          <div className="flex flex-wrap gap-2">
            {profile.connectionIntents.map((intent, index) => (
              <div
                key={index}
                className="px-3 py-1 bg-pink-500/20 border border-pink-500/30 rounded-full text-pink-400 text-sm"
              >
                {intent.type.charAt(0).toUpperCase() + intent.type.slice(1)}
              </div>
            ))}
          </div>
        </div>

        {/* Details Section */}
        <div
          ref={detailsRef}
          data-track="details"
          className="py-6"
        >
          <h2 className="text-xl font-semibold text-white mb-4">Details</h2>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 text-gray-300">
              <Briefcase className="h-5 w-5 text-pink-400" />
              <span>UX Designer</span>
            </div>
            <div className="flex items-center space-x-3 text-gray-300">
              <GraduationCap className="h-5 w-5 text-pink-400" />
              <span>Stanford University</span>
            </div>
            <div className="flex items-center space-x-3 text-gray-300">
              <Calendar className="h-5 w-5 text-pink-400" />
              <span>Joined {new Date(profile.createdAt).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Interests */}
        <div className="py-6">
          <h2 className="text-xl font-semibold text-white mb-4">Interests</h2>
          <div className="grid grid-cols-2 gap-3">
            {[
              { icon: Coffee, label: 'Coffee' },
              { icon: Book, label: 'Reading' },
              { icon: Camera, label: 'Photography' },
              { icon: Music, label: 'Music' },
              { icon: Palette, label: 'Art' },
              { icon: Users, label: 'Socializing' }
            ].map((interest, index) => (
              <div
                key={index}
                className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg"
              >
                <interest.icon className="h-5 w-5 text-pink-400" />
                <span className="text-gray-300">{interest.label}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-black/90 backdrop-blur-sm border-t border-gray-800">
        <div className="flex justify-center space-x-6">
          <Button
            onClick={() => handleAction('pass')}
            size="lg"
            variant="outline"
            className="w-16 h-16 rounded-full border-gray-600 text-gray-400 hover:bg-gray-800 hover:text-white"
          >
            <X className="h-6 w-6" />
          </Button>

          <Button
            onClick={() => handleAction('like')}
            size="lg"
            className="w-16 h-16 rounded-full impression-button"
          >
            <Heart className="h-6 w-6" />
          </Button>
        </div>
      </div>

      {/* Debug Info (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-20 right-4 p-2 bg-gray-900/90 rounded text-xs text-gray-300 max-w-xs">
          <div>Dwell: {Math.round(dwellTime / 1000)}s</div>
          <div>Scroll: {Math.round(scrollBehavior.totalDistance)}px</div>
          <div>Interactions: {interactionDepth}</div>
          <div>Focus Areas: {focusAreas.length}</div>
          <div>Device: {deviceCapabilities.tier}</div>
        </div>
      )}
    </div>
  );
};

export default ProfileView;
