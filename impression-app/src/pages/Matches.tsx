import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Heart } from 'lucide-react';

const Matches: React.FC = () => {
  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="max-w-md w-full"
      >
        <Card className="impression-card">
          <CardHeader className="text-center">
            <Heart className="h-12 w-12 text-pink-400 mx-auto mb-4" />
            <CardTitle className="text-2xl text-white">
              Matches & Dynamics
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-300">
              This is where the matching and relationship dynamics will be displayed, featuring:
            </p>
            <ul className="text-left text-gray-400 space-y-2">
              <li>• Dynamic match type determination</li>
              <li>• 48-hour window management</li>
              <li>• Progressive identity revelation</li>
              <li>• Relationship formation tracking</li>
              <li>• Unit Profile creation and management</li>
            </ul>
            <Button className="impression-button w-full mt-6">
              Coming Soon
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default Matches;
