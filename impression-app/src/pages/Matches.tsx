import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Heart,
  Users,
  Briefcase,
  Palette,
  HelpCircle,
  Clock,
  Sparkles,
  ArrowLeft,
  MessageCircle,
  X,
  Check,
  Timer,
  Star,
  Zap,
  Eye,
  Settings
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import type { Match, MatchType, DynamicRelationship } from '@/types';
import { formatTimeRemaining } from '@/lib/utils';

const Matches: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'matches' | 'dynamics'>('matches');
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [showMatchReveal, setShowMatchReveal] = useState(false);

  // Mock matches data
  const [matches, setMatches] = useState<Match[]>([
    {
      id: 'match_1',
      users: [user?.id || '', 'user_2'],
      matchType: 'first-flirt',
      similarity: 0.87,
      confidence: 0.92,
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      expiresAt: new Date(Date.now() + 46 * 60 * 60 * 1000), // 46 hours remaining
      status: 'pending',
      revealLevel: 'anonymous'
    },
    {
      id: 'match_2',
      users: [user?.id || '', 'user_3'],
      matchType: 'first-collab',
      similarity: 0.79,
      confidence: 0.85,
      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
      expiresAt: new Date(Date.now() + 40 * 60 * 60 * 1000), // 40 hours remaining
      status: 'pending',
      revealLevel: 'anonymous'
    },
    {
      id: 'match_3',
      users: [user?.id || '', 'user_4'],
      matchType: 'first-meet',
      similarity: 0.74,
      confidence: 0.78,
      createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
      expiresAt: new Date(Date.now() + 36 * 60 * 60 * 1000), // 36 hours remaining
      status: 'accepted',
      revealLevel: 'partial'
    }
  ]);

  // Mock dynamics data
  const [dynamics, setDynamics] = useState<DynamicRelationship[]>([
    {
      id: 'dynamic_1',
      participants: [user?.id || '', 'user_4'],
      name: 'Creative Collaborators',
      type: 'creative',
      isUnitProfile: false,
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      lastInteraction: new Date(Date.now() - 2 * 60 * 60 * 1000),
      status: 'active'
    }
  ]);

  const getMatchTypeIcon = (type: MatchType) => {
    switch (type) {
      case 'first-flirt': return Heart;
      case 'first-meet': return Users;
      case 'first-collab': return Briefcase;
      case 'first-sync': return Palette;
      default: return HelpCircle;
    }
  };

  const getMatchTypeLabel = (type: MatchType) => {
    switch (type) {
      case 'first-flirt': return 'First Flirt';
      case 'first-meet': return 'First Meet';
      case 'first-collab': return 'First Collab';
      case 'first-sync': return 'First Sync';
      default: return 'First Encounter';
    }
  };

  const getMatchTypeDescription = (type: MatchType) => {
    switch (type) {
      case 'first-flirt': return 'Romantic resonance detected';
      case 'first-meet': return 'Platonic connection emerging';
      case 'first-collab': return 'Collaborative potential identified';
      case 'first-sync': return 'Creative synchronicity found';
      default: return 'Undefined connection pattern';
    }
  };

  const getMatchTypeColor = (type: MatchType) => {
    switch (type) {
      case 'first-flirt': return 'from-pink-500 to-red-500';
      case 'first-meet': return 'from-blue-500 to-cyan-500';
      case 'first-collab': return 'from-green-500 to-emerald-500';
      case 'first-sync': return 'from-purple-500 to-indigo-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const handleMatchAction = (match: Match, action: 'accept' | 'decline') => {
    if (action === 'accept') {
      setSelectedMatch(match);
      setShowMatchReveal(true);
    } else {
      setMatches(prev => prev.filter(m => m.id !== match.id));
    }
  };

  const handleRevealComplete = () => {
    if (selectedMatch) {
      setMatches(prev => prev.map(m =>
        m.id === selectedMatch.id
          ? { ...m, status: 'accepted', revealLevel: 'partial' }
          : m
      ));

      // Create new dynamic relationship
      const newDynamic: DynamicRelationship = {
        id: `dynamic_${Date.now()}`,
        participants: selectedMatch.users,
        type: selectedMatch.matchType.replace('first-', '') as any,
        isUnitProfile: false,
        createdAt: new Date(),
        lastInteraction: new Date(),
        status: 'forming'
      };

      setDynamics(prev => [...prev, newDynamic]);
    }

    setShowMatchReveal(false);
    setSelectedMatch(null);
  };

  const MatchRevealModal: React.FC<{ match: Match }> = ({ match }) => {
    const [revealStage, setRevealStage] = useState(0);
    const Icon = getMatchTypeIcon(match.matchType);

    useEffect(() => {
      const timer = setTimeout(() => {
        if (revealStage < 3) {
          setRevealStage(prev => prev + 1);
        }
      }, 1500);

      return () => clearTimeout(timer);
    }, [revealStage]);

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          className="max-w-md w-full"
        >
          <Card className="impression-card">
            <CardContent className="p-8 text-center">
              <AnimatePresence mode="wait">
                {revealStage === 0 && (
                  <motion.div
                    key="stage0"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <Sparkles className="h-16 w-16 text-pink-400 mx-auto mb-4 animate-pulse" />
                    <h3 className="text-2xl font-bold text-white mb-2">
                      Resonance Detected
                    </h3>
                    <p className="text-gray-300">
                      A meaningful connection has emerged...
                    </p>
                  </motion.div>
                )}

                {revealStage === 1 && (
                  <motion.div
                    key="stage1"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${getMatchTypeColor(match.matchType)} flex items-center justify-center`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-2">
                      {getMatchTypeLabel(match.matchType)}
                    </h3>
                    <p className="text-gray-300">
                      {getMatchTypeDescription(match.matchType)}
                    </p>
                  </motion.div>
                )}

                {revealStage === 2 && (
                  <motion.div
                    key="stage2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <div className="space-y-4">
                      <div className="flex items-center justify-center space-x-2">
                        <Star className="h-5 w-5 text-yellow-400" />
                        <span className="text-white font-medium">
                          {Math.round(match.similarity * 100)}% Compatibility
                        </span>
                      </div>
                      <div className="flex items-center justify-center space-x-2">
                        <Zap className="h-5 w-5 text-pink-400" />
                        <span className="text-white font-medium">
                          {Math.round(match.confidence * 100)}% Confidence
                        </span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <motion.div
                          className="h-2 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${match.similarity * 100}%` }}
                          transition={{ duration: 1 }}
                        />
                      </div>
                    </div>
                  </motion.div>
                )}

                {revealStage === 3 && (
                  <motion.div
                    key="stage3"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="space-y-6"
                  >
                    <div>
                      <h3 className="text-xl font-bold text-white mb-2">
                        Ready to Connect?
                      </h3>
                      <p className="text-gray-300 text-sm">
                        You have 48 hours to explore this connection.
                        Identity will be revealed progressively as you both engage.
                      </p>
                    </div>

                    <div className="flex space-x-3">
                      <Button
                        onClick={() => setShowMatchReveal(false)}
                        variant="outline"
                        className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
                      >
                        Maybe Later
                      </Button>
                      <Button
                        onClick={handleRevealComplete}
                        className="flex-1 impression-button"
                      >
                        Let's Connect
                      </Button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    );
  };

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/map')}
              className="text-gray-400 hover:text-white"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-bold impression-text-gradient">
              Connections
            </h1>
          </div>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate('/settings')}
            className="text-gray-400 hover:text-white"
          >
            <Settings className="h-5 w-5" />
          </Button>
        </div>
      </header>

      {/* Tabs */}
      <div className="border-b border-gray-800">
        <div className="flex">
          <button
            onClick={() => setActiveTab('matches')}
            className={`flex-1 py-4 px-6 text-center font-medium transition-colors ${
              activeTab === 'matches'
                ? 'text-pink-400 border-b-2 border-pink-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Matches ({matches.filter(m => m.status === 'pending').length})
          </button>
          <button
            onClick={() => setActiveTab('dynamics')}
            className={`flex-1 py-4 px-6 text-center font-medium transition-colors ${
              activeTab === 'dynamics'
                ? 'text-pink-400 border-b-2 border-pink-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Dynamics ({dynamics.length})
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-4">
        {activeTab === 'matches' && (
          <>
            {matches.filter(m => m.status === 'pending').length === 0 ? (
              <div className="text-center py-12">
                <Heart className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-400 mb-2">
                  No Active Matches
                </h3>
                <p className="text-gray-500 mb-6">
                  Keep exploring the Resonance Map to discover new connections
                </p>
                <Button
                  onClick={() => navigate('/map')}
                  className="impression-button"
                >
                  Explore Map
                </Button>
              </div>
            ) : (
              matches
                .filter(m => m.status === 'pending')
                .map((match) => {
                  const Icon = getMatchTypeIcon(match.matchType);
                  const timeRemaining = match.expiresAt.getTime() - Date.now();

                  return (
                    <motion.div
                      key={match.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      layout
                    >
                      <Card className="impression-card">
                        <CardContent className="p-6">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center space-x-3">
                              <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${getMatchTypeColor(match.matchType)} flex items-center justify-center`}>
                                <Icon className="h-6 w-6 text-white" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-white">
                                  {getMatchTypeLabel(match.matchType)}
                                </h3>
                                <p className="text-sm text-gray-400">
                                  {getMatchTypeDescription(match.matchType)}
                                </p>
                              </div>
                            </div>

                            <div className="text-right">
                              <div className="flex items-center space-x-1 text-pink-400 text-sm">
                                <Timer className="h-4 w-4" />
                                <span>{formatTimeRemaining(timeRemaining)}</span>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-3 mb-4">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-400">Compatibility</span>
                              <span className="text-white">{Math.round(match.similarity * 100)}%</span>
                            </div>
                            <div className="w-full bg-gray-700 rounded-full h-2">
                              <div
                                className="h-2 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full"
                                style={{ width: `${match.similarity * 100}%` }}
                              />
                            </div>
                          </div>

                          <div className="flex space-x-3">
                            <Button
                              onClick={() => handleMatchAction(match, 'decline')}
                              variant="outline"
                              size="sm"
                              className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
                            >
                              <X className="h-4 w-4 mr-2" />
                              Pass
                            </Button>
                            <Button
                              onClick={() => handleMatchAction(match, 'accept')}
                              size="sm"
                              className="flex-1 impression-button"
                            >
                              <Check className="h-4 w-4 mr-2" />
                              Connect
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })
            )}
          </>
        )}

        {activeTab === 'dynamics' && (
          <>
            {dynamics.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-400 mb-2">
                  No Active Dynamics
                </h3>
                <p className="text-gray-500">
                  Accept matches to start building dynamic relationships
                </p>
              </div>
            ) : (
              dynamics.map((dynamic) => (
                <motion.div
                  key={dynamic.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  layout
                >
                  <Card className="impression-card">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="font-semibold text-white">
                            {dynamic.name || `${dynamic.type.charAt(0).toUpperCase() + dynamic.type.slice(1)} Connection`}
                          </h3>
                          <p className="text-sm text-gray-400">
                            {dynamic.isUnitProfile ? 'Unit Profile' : 'Individual Connection'}
                          </p>
                        </div>

                        <div className={`px-2 py-1 rounded-full text-xs ${
                          dynamic.status === 'active' ? 'bg-green-500/20 text-green-400' :
                          dynamic.status === 'forming' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {dynamic.status}
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                        <span>Last interaction: {new Date(dynamic.lastInteraction).toLocaleDateString()}</span>
                        <span>{dynamic.participants.length} participants</span>
                      </div>

                      <Button
                        size="sm"
                        className="w-full impression-button"
                      >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Continue Connection
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            )}
          </>
        )}
      </div>

      {/* Match Reveal Modal */}
      <AnimatePresence>
        {showMatchReveal && selectedMatch && (
          <MatchRevealModal match={selectedMatch} />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Matches;
