import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Sparkles,
  Camera,
  Mic,
  Heart,
  Users,
  Briefcase,
  Palette,
  HelpCircle,
  ChevronRight,
  ChevronLeft,
  Upload,
  Play,
  Pause,
  RotateCcw,
  Check,
  Shield,
  Eye,
  MapPin
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import type { ConnectionIntent, OnboardingData } from '@/types';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  component: React.ComponentType<any>;
}

const Onboarding: React.FC = () => {
  const { user, updateUser } = useAuth();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [onboardingData, setOnboardingData] = useState<Partial<OnboardingData>>({
    personalDetails: {
      name: user?.name || '',
      age: 0,
      pronouns: '',
      location: ''
    },
    photos: [],
    bio: '',
    connectionIntents: [],
    privacyConsent: {
      behavioralTracking: false,
      locationSharing: false,
      voiceAnalysis: false,
      dataProcessing: false
    }
  });

  const updateOnboardingData = (field: string, value: any) => {
    setOnboardingData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updatePersonalDetails = (field: string, value: any) => {
    setOnboardingData(prev => ({
      ...prev,
      personalDetails: {
        ...prev.personalDetails!,
        [field]: value
      }
    }));
  };

  const updatePrivacyConsent = (field: string, value: boolean) => {
    setOnboardingData(prev => ({
      ...prev,
      privacyConsent: {
        ...prev.privacyConsent!,
        [field]: value
      }
    }));
  };

  // Welcome Step
  const WelcomeStep: React.FC = () => (
    <div className="text-center space-y-6">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.6, type: "spring" }}
      >
        <Sparkles className="h-16 w-16 text-pink-400 mx-auto mb-4" />
      </motion.div>
      <h2 className="text-3xl font-bold impression-text-gradient">
        Welcome to Impression
      </h2>
      <p className="text-gray-300 text-lg">
        This is not a dating app. It's a resonance-based system where relationships emerge through behavioral patterns, not declarations.
      </p>
      <div className="bg-gray-800/50 p-4 rounded-lg">
        <p className="text-pink-400 font-medium mb-2">Core Philosophy:</p>
        <p className="text-gray-300 text-sm">
          "Users are never forced into categories—they are revealed through pattern and reflection."
        </p>
      </div>
    </div>
  );

  // Personal Details Step
  const PersonalDetailsStep: React.FC = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Tell us about yourself</h2>
        <p className="text-gray-400">Basic information to get started</p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-white mb-2">Name</label>
          <Input
            value={onboardingData.personalDetails?.name || ''}
            onChange={(e) => updatePersonalDetails('name', e.target.value)}
            placeholder="Your name"
            className="bg-gray-800 border-gray-600 text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white mb-2">Age</label>
          <Input
            type="number"
            value={onboardingData.personalDetails?.age || ''}
            onChange={(e) => updatePersonalDetails('age', parseInt(e.target.value))}
            placeholder="Your age"
            className="bg-gray-800 border-gray-600 text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white mb-2">Pronouns (Optional)</label>
          <Input
            value={onboardingData.personalDetails?.pronouns || ''}
            onChange={(e) => updatePersonalDetails('pronouns', e.target.value)}
            placeholder="e.g., they/them, she/her, he/him"
            className="bg-gray-800 border-gray-600 text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white mb-2">General Location</label>
          <Input
            value={onboardingData.personalDetails?.location || ''}
            onChange={(e) => updatePersonalDetails('location', e.target.value)}
            placeholder="e.g., San Francisco, CA"
            className="bg-gray-800 border-gray-600 text-white"
          />
          <p className="text-xs text-gray-500 mt-1">
            We'll abstract this to privacy-focused zones
          </p>
        </div>
      </div>
    </div>
  );

  // Photo Upload Step
  const PhotoUploadStep: React.FC = () => {
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (files) {
        const newPhotos = Array.from(files).map(file => URL.createObjectURL(file));
        updateOnboardingData('photos', [...(onboardingData.photos || []), ...newPhotos]);
      }
    };

    return (
      <div className="space-y-6">
        <div className="text-center">
          <Camera className="h-12 w-12 text-pink-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Add Your Photos</h2>
          <p className="text-gray-400">Show different aspects of your personality</p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {onboardingData.photos?.map((photo, index) => (
            <div key={index} className="aspect-square bg-gray-800 rounded-lg overflow-hidden">
              <img src={photo} alt={`Photo ${index + 1}`} className="w-full h-full object-cover" />
            </div>
          ))}

          {Array.from({ length: Math.max(0, 6 - (onboardingData.photos?.length || 0)) }).map((_, index) => (
            <button
              key={index}
              onClick={() => fileInputRef.current?.click()}
              className="aspect-square bg-gray-800 border-2 border-dashed border-gray-600 rounded-lg flex flex-col items-center justify-center hover:border-pink-500 transition-colors"
            >
              <Upload className="h-8 w-8 text-gray-400 mb-2" />
              <span className="text-sm text-gray-400">Add Photo</span>
            </button>
          ))}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handlePhotoUpload}
          className="hidden"
        />

        <div className="bg-gray-800/50 p-4 rounded-lg">
          <h3 className="text-pink-400 font-medium mb-2">Photo Tips:</h3>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• Show variety - different settings and expressions</li>
            <li>• Include at least one clear face photo</li>
            <li>• Avoid group photos as your main image</li>
            <li>• Natural lighting works best</li>
          </ul>
        </div>
      </div>
    );
  };

  // Bio Step
  const BioStep: React.FC = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Express Yourself</h2>
        <p className="text-gray-400">Write something that captures your essence</p>
      </div>

      <div>
        <textarea
          value={onboardingData.bio || ''}
          onChange={(e) => updateOnboardingData('bio', e.target.value)}
          placeholder="Tell us about yourself, your interests, what makes you unique..."
          className="w-full h-32 bg-gray-800 border border-gray-600 rounded-lg p-3 text-white placeholder-gray-400 resize-none focus:border-pink-500"
          maxLength={500}
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>Be authentic - this helps our AI understand your tone</span>
          <span>{onboardingData.bio?.length || 0}/500</span>
        </div>
      </div>

      <div className="bg-gray-800/50 p-4 rounded-lg">
        <h3 className="text-pink-400 font-medium mb-2">AI Analysis:</h3>
        <p className="text-sm text-gray-300">
          Our AI will analyze your bio for humor, confidence, vulnerability, and creativity to help find compatible connections. This analysis remains private and is only used for matching.
        </p>
      </div>
    </div>
  );

  // Connection Intents Step
  const ConnectionIntentsStep: React.FC = () => {
    const intentOptions = [
      { type: 'romantic', icon: Heart, label: 'Romantic', description: 'Looking for romantic connections' },
      { type: 'platonic', icon: Users, label: 'Platonic', description: 'Seeking meaningful friendships' },
      { type: 'collaborative', icon: Briefcase, label: 'Collaborative', description: 'Professional or project partnerships' },
      { type: 'creative', icon: Palette, label: 'Creative', description: 'Artistic collaborations and inspiration' },
      { type: 'undefined', icon: HelpCircle, label: 'Open to Discovery', description: 'Let connections emerge naturally' }
    ];

    const toggleIntent = (type: string) => {
      const currentIntents = onboardingData.connectionIntents || [];
      const existingIndex = currentIntents.findIndex(intent => intent.type === type);

      if (existingIndex >= 0) {
        const newIntents = currentIntents.filter(intent => intent.type !== type);
        updateOnboardingData('connectionIntents', newIntents);
      } else {
        const newIntent: ConnectionIntent = {
          type: type as any,
          intensity: 0.7,
          description: ''
        };
        updateOnboardingData('connectionIntents', [...currentIntents, newIntent]);
      }
    };

    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2">Connection Intentions</h2>
          <p className="text-gray-400">What kinds of connections are you open to? (Select multiple)</p>
        </div>

        <div className="space-y-3">
          {intentOptions.map((option) => {
            const isSelected = onboardingData.connectionIntents?.some(intent => intent.type === option.type);
            const Icon = option.icon;

            return (
              <button
                key={option.type}
                onClick={() => toggleIntent(option.type)}
                className={`w-full p-4 rounded-lg border-2 transition-all ${
                  isSelected
                    ? 'border-pink-500 bg-pink-500/10'
                    : 'border-gray-600 bg-gray-800/50 hover:border-gray-500'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <Icon className={`h-6 w-6 ${isSelected ? 'text-pink-400' : 'text-gray-400'}`} />
                  <div className="text-left">
                    <h3 className={`font-medium ${isSelected ? 'text-pink-400' : 'text-white'}`}>
                      {option.label}
                    </h3>
                    <p className="text-sm text-gray-400">{option.description}</p>
                  </div>
                  {isSelected && <Check className="h-5 w-5 text-pink-400 ml-auto" />}
                </div>
              </button>
            );
          })}
        </div>

        <div className="bg-gray-800/50 p-4 rounded-lg">
          <p className="text-sm text-gray-300">
            <span className="text-pink-400 font-medium">Remember:</span> These are intentions, not rigid categories.
            Relationships will emerge naturally through behavioral resonance.
          </p>
        </div>
      </div>
    );
  };

  // Voice Capture Step
  const VoiceCaptureStep: React.FC = () => {
    const [isRecording, setIsRecording] = useState(false);
    const [hasRecording, setHasRecording] = useState(false);

    const startRecording = () => {
      setIsRecording(true);
      // Simulate recording
      setTimeout(() => {
        setIsRecording(false);
        setHasRecording(true);
      }, 3000);
    };

    return (
      <div className="space-y-6">
        <div className="text-center">
          <Mic className="h-12 w-12 text-pink-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Voice Tone Capture</h2>
          <p className="text-gray-400">Optional: Help us understand your communication style</p>
        </div>

        <div className="bg-gray-800/50 p-4 rounded-lg">
          <h3 className="text-pink-400 font-medium mb-2">Sample Reading:</h3>
          <p className="text-gray-300 text-sm italic">
            "I believe that authentic connections happen when we're genuinely ourselves.
            There's something beautiful about discovering compatibility through natural interaction
            rather than forced categories."
          </p>
        </div>

        <div className="text-center">
          {!hasRecording ? (
            <Button
              onClick={startRecording}
              disabled={isRecording}
              className={`impression-button ${isRecording ? 'opacity-50' : ''}`}
            >
              {isRecording ? (
                <>
                  <Pause className="h-4 w-4 mr-2" />
                  Recording... {/* Add timer */}
                </>
              ) : (
                <>
                  <Mic className="h-4 w-4 mr-2" />
                  Start Recording
                </>
              )}
            </Button>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-center space-x-4">
                <Button variant="outline" onClick={() => setHasRecording(false)}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Re-record
                </Button>
                <Button className="impression-button">
                  <Check className="h-4 w-4 mr-2" />
                  Use Recording
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="text-center">
          <Button variant="ghost" onClick={() => setCurrentStep(currentStep + 1)}>
            Skip for now
          </Button>
        </div>

        <div className="bg-gray-800/50 p-4 rounded-lg">
          <h3 className="text-pink-400 font-medium mb-2">Privacy Note:</h3>
          <p className="text-sm text-gray-300">
            Voice analysis happens on-device when possible. We analyze tone, pace, and emotional patterns
            to improve matching quality. You can delete this data anytime.
          </p>
        </div>
      </div>
    );
  };

  // Privacy Consent Step
  const PrivacyConsentStep: React.FC = () => {
    const privacyOptions = [
      {
        key: 'behavioralTracking',
        icon: Eye,
        title: 'Behavioral Tracking',
        description: 'Track how you interact with profiles to improve matching',
        required: true
      },
      {
        key: 'locationSharing',
        icon: MapPin,
        title: 'Location Abstraction',
        description: 'Use your general location for zone-based matching',
        required: true
      },
      {
        key: 'voiceAnalysis',
        icon: Mic,
        title: 'Voice Analysis',
        description: 'Analyze voice tone for compatibility (if voice sample provided)',
        required: false
      },
      {
        key: 'dataProcessing',
        icon: Shield,
        title: 'Data Processing',
        description: 'Process your data to generate behavioral vectors and matches',
        required: true
      }
    ];

    return (
      <div className="space-y-6">
        <div className="text-center">
          <Shield className="h-12 w-12 text-pink-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Privacy & Consent</h2>
          <p className="text-gray-400">Control how your data is used</p>
        </div>

        <div className="space-y-4">
          {privacyOptions.map((option) => {
            const Icon = option.icon;
            const isChecked = onboardingData.privacyConsent?.[option.key as keyof typeof onboardingData.privacyConsent];

            return (
              <div key={option.key} className="flex items-start space-x-3 p-4 bg-gray-800/50 rounded-lg">
                <button
                  onClick={() => updatePrivacyConsent(option.key, !isChecked)}
                  disabled={option.required}
                  className={`flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center ${
                    isChecked || option.required
                      ? 'bg-pink-500 border-pink-500'
                      : 'border-gray-400'
                  } ${option.required ? 'opacity-50' : 'cursor-pointer'}`}
                >
                  {(isChecked || option.required) && <Check className="h-3 w-3 text-white" />}
                </button>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <Icon className="h-4 w-4 text-pink-400" />
                    <h3 className="font-medium text-white">{option.title}</h3>
                    {option.required && (
                      <span className="text-xs bg-pink-500/20 text-pink-400 px-2 py-1 rounded">
                        Required
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-400">{option.description}</p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="bg-gray-800/50 p-4 rounded-lg">
          <h3 className="text-pink-400 font-medium mb-2">Our Privacy Promise:</h3>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• Your precise location is never shared</li>
            <li>• You remain anonymous until mutual interest</li>
            <li>• You can delete your data anytime</li>
            <li>• No data is sold to third parties</li>
            <li>• Behavioral tracking improves your experience</li>
          </ul>
        </div>
      </div>
    );
  };

  const steps: OnboardingStep[] = [
    { id: 'welcome', title: 'Welcome', description: 'Introduction to Impression', component: WelcomeStep },
    { id: 'personal', title: 'Personal Details', description: 'Basic information', component: PersonalDetailsStep },
    { id: 'photos', title: 'Photos', description: 'Add your photos', component: PhotoUploadStep },
    { id: 'bio', title: 'Bio', description: 'Express yourself', component: BioStep },
    { id: 'intents', title: 'Intentions', description: 'Connection preferences', component: ConnectionIntentsStep },
    { id: 'voice', title: 'Voice', description: 'Tone capture', component: VoiceCaptureStep },
    { id: 'privacy', title: 'Privacy', description: 'Consent & settings', component: PrivacyConsentStep }
  ];

  const currentStepData = steps[currentStep];
  const CurrentStepComponent = currentStepData.component;

  const canProceed = () => {
    switch (currentStep) {
      case 1: // Personal Details
        return onboardingData.personalDetails?.name &&
               onboardingData.personalDetails?.age &&
               onboardingData.personalDetails?.location;
      case 2: // Photos
        return (onboardingData.photos?.length || 0) >= 1;
      case 3: // Bio
        return (onboardingData.bio?.length || 0) >= 50;
      case 4: // Intents
        return (onboardingData.connectionIntents?.length || 0) >= 1;
      case 6: // Privacy
        return onboardingData.privacyConsent?.behavioralTracking &&
               onboardingData.privacyConsent?.locationSharing &&
               onboardingData.privacyConsent?.dataProcessing;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleComplete = async () => {
    try {
      // Update user with onboarding completion
      await updateUser({
        onboardingComplete: true,
        photos: onboardingData.photos || [],
        bio: onboardingData.bio,
        connectionIntents: onboardingData.connectionIntents || [],
        privacySettings: {
          locationPrecision: 'zone',
          behavioralTracking: onboardingData.privacyConsent?.behavioralTracking || false,
          voiceAnalysis: onboardingData.privacyConsent?.voiceAnalysis || false,
          photoAnalysis: true,
          dataRetention: 30,
          anonymousMode: false
        }
      });

      navigate('/map');
    } catch (error) {
      console.error('Error completing onboarding:', error);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Background Animation */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-pink-500/10 via-purple-500/10 to-indigo-500/10" />
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </div>

      {/* Progress Bar */}
      <div className="relative z-10 p-4">
        <div className="max-w-md mx-auto">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-400">Step {currentStep + 1} of {steps.length}</span>
            <span className="text-sm text-gray-400">{Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-800 rounded-full h-2">
            <motion.div
              className="h-2 impression-gradient rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <p className="text-center text-sm text-gray-400 mt-2">{currentStepData.description}</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <Card className="impression-card">
            <CardHeader>
              <CardTitle className="text-center text-white">
                {currentStepData.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <CurrentStepComponent />
                </motion.div>
              </AnimatePresence>
            </CardContent>
          </Card>

          {/* Navigation */}
          <div className="flex justify-between mt-6">
            <Button
              variant="ghost"
              onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
              disabled={currentStep === 0}
              className="text-gray-400 hover:text-white"
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            <Button
              onClick={handleNext}
              disabled={!canProceed()}
              className="impression-button"
            >
              {currentStep === steps.length - 1 ? 'Complete' : 'Next'}
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
