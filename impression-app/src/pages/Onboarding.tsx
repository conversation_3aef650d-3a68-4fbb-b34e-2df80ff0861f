import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Sparkles } from 'lucide-react';

const Onboarding: React.FC = () => {
  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="max-w-md w-full"
      >
        <Card className="impression-card">
          <CardHeader className="text-center">
            <Sparkles className="h-12 w-12 text-pink-400 mx-auto mb-4" />
            <CardTitle className="text-2xl text-white">
              Onboarding Experience
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-300">
              This is where the comprehensive onboarding flow will be implemented, including:
            </p>
            <ul className="text-left text-gray-400 space-y-2">
              <li>• Profile creation with behavioral intent capture</li>
              <li>• Photo guidance system</li>
              <li>• Voice tone capture</li>
              <li>• Connection intent selection</li>
              <li>• Privacy settings configuration</li>
              <li>• Conceptual orientation to resonance-based matching</li>
            </ul>
            <Button className="impression-button w-full mt-6">
              Coming Soon
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default Onboarding;
