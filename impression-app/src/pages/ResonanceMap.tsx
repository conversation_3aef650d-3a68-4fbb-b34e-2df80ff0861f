import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Map,
  ZoomIn,
  ZoomOut,
  Users,
  Settings,
  Heart,
  MessageCircle,
  User,
  Filter,
  Search,
  MapPin,
  Activity
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import type { BubbleGridUser, LocationZone } from '@/types';
import { detectDeviceCapabilities, abstractLocation } from '@/lib/utils';

const ResonanceMap: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [selectedZone, setSelectedZone] = useState<LocationZone | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [bubbleUsers, setBubbleUsers] = useState<BubbleGridUser[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [deviceCapabilities] = useState(detectDeviceCapabilities());

  // Mock zones data
  const zones: LocationZone[] = [
    {
      id: 'downtown_sf',
      name: 'Downtown SF',
      bounds: { north: 37.8, south: 37.75, east: -122.4, west: -122.45 },
      center: { lat: 37.775, lng: -122.425 },
      userCount: 127,
      density: 0.8
    },
    {
      id: 'mission_sf',
      name: 'Mission District',
      bounds: { north: 37.77, south: 37.74, east: -122.4, west: -122.43 },
      center: { lat: 37.755, lng: -122.415 },
      userCount: 89,
      density: 0.6
    },
    {
      id: 'soma_sf',
      name: 'SOMA',
      bounds: { north: 37.78, south: 37.76, east: -122.39, west: -122.42 },
      center: { lat: 37.77, lng: -122.405 },
      userCount: 156,
      density: 0.9
    }
  ];

  // Generate mock bubble users for selected zone
  const generateBubbleUsers = useCallback((zone: LocationZone): BubbleGridUser[] => {
    const users: BubbleGridUser[] = [];
    const count = Math.min(zone.userCount, 50); // Limit for performance

    for (let i = 0; i < count; i++) {
      users.push({
        id: `user_${i}`,
        anonymousId: `anon_${Math.random().toString(36).substr(2, 9)}`,
        position: {
          x: Math.random() * 300 + 50,
          y: Math.random() * 300 + 50
        },
        size: Math.random() * 30 + 20, // 20-50px
        color: `hsl(${Math.random() * 60 + 300}, 70%, 60%)`, // Pink to purple range
        activity: Math.random(),
        compatibility: Math.random(),
        isUnitProfile: Math.random() > 0.9 // 10% chance of unit profile
      });
    }

    return users;
  }, []);

  // Canvas drawing functions
  const drawBubbleGrid = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background gradient
    const gradient = ctx.createRadialGradient(
      canvas.width / 2, canvas.height / 2, 0,
      canvas.width / 2, canvas.height / 2, canvas.width / 2
    );
    gradient.addColorStop(0, 'rgba(236, 72, 153, 0.1)');
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0.8)');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw bubbles
    bubbleUsers.forEach((bubble, index) => {
      const x = bubble.position.x * zoomLevel + panOffset.x;
      const y = bubble.position.y * zoomLevel + panOffset.y;
      const radius = bubble.size * zoomLevel;

      // Skip if outside viewport
      if (x < -radius || x > canvas.width + radius || y < -radius || y > canvas.height + radius) {
        return;
      }

      // Bubble glow effect
      const glowGradient = ctx.createRadialGradient(x, y, 0, x, y, radius * 1.5);
      glowGradient.addColorStop(0, bubble.color + '80');
      glowGradient.addColorStop(1, bubble.color + '00');
      ctx.fillStyle = glowGradient;
      ctx.fillRect(x - radius * 1.5, y - radius * 1.5, radius * 3, radius * 3);

      // Main bubble
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fillStyle = bubble.color + (bubble.compatibility ? Math.floor(bubble.compatibility * 255).toString(16).padStart(2, '0') : '80');
      ctx.fill();

      // Activity indicator
      if (bubble.activity > 0.7) {
        ctx.beginPath();
        ctx.arc(x + radius * 0.6, y - radius * 0.6, radius * 0.2, 0, Math.PI * 2);
        ctx.fillStyle = '#10B981';
        ctx.fill();
      }

      // Unit profile indicator
      if (bubble.isUnitProfile) {
        ctx.beginPath();
        ctx.arc(x - radius * 0.6, y - radius * 0.6, radius * 0.15, 0, Math.PI * 2);
        ctx.fillStyle = '#F59E0B';
        ctx.fill();

        ctx.beginPath();
        ctx.arc(x - radius * 0.3, y - radius * 0.6, radius * 0.15, 0, Math.PI * 2);
        ctx.fillStyle = '#F59E0B';
        ctx.fill();
      }
    });
  }, [bubbleUsers, zoomLevel, panOffset]);

  // Handle canvas interactions
  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Find clicked bubble
    const clickedBubble = bubbleUsers.find(bubble => {
      const bubbleX = bubble.position.x * zoomLevel + panOffset.x;
      const bubbleY = bubble.position.y * zoomLevel + panOffset.y;
      const distance = Math.sqrt((x - bubbleX) ** 2 + (y - bubbleY) ** 2);
      return distance <= bubble.size * zoomLevel;
    });

    if (clickedBubble) {
      navigate(`/profile/${clickedBubble.id}`);
    }
  };

  // Zoom controls
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev * 1.2, 3));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev / 1.2, 0.5));
  };

  // Zone selection
  const handleZoneSelect = (zone: LocationZone) => {
    setSelectedZone(zone);
    setBubbleUsers(generateBubbleUsers(zone));
    setZoomLevel(1);
    setPanOffset({ x: 0, y: 0 });
  };

  // Initialize with first zone
  useEffect(() => {
    if (zones.length > 0 && !selectedZone) {
      handleZoneSelect(zones[0]);
    }
  }, [zones, selectedZone]);

  // Redraw canvas when dependencies change
  useEffect(() => {
    drawBubbleGrid();
  }, [drawBubbleGrid]);

  // Handle canvas resize
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resizeCanvas = () => {
      const container = canvas.parentElement;
      if (container) {
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;
        drawBubbleGrid();
      }
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    return () => window.removeEventListener('resize', resizeCanvas);
  }, [drawBubbleGrid]);

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      {/* Header */}
      <header className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold impression-text-gradient">Impression</h1>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <MapPin className="h-4 w-4" />
              <span>{selectedZone?.name || 'Select Zone'}</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowFilters(!showFilters)}
              className="text-gray-400 hover:text-white"
            >
              <Filter className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/matches')}
              className="text-gray-400 hover:text-white"
            >
              <Heart className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/settings')}
              className="text-gray-400 hover:text-white"
            >
              <Settings className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </header>

      {/* Zone Selector */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex space-x-2 overflow-x-auto">
          {zones.map((zone) => (
            <Button
              key={zone.id}
              variant={selectedZone?.id === zone.id ? "default" : "outline"}
              onClick={() => handleZoneSelect(zone)}
              className={`flex-shrink-0 ${
                selectedZone?.id === zone.id
                  ? 'impression-button'
                  : 'border-gray-600 text-gray-300 hover:bg-gray-800'
              }`}
            >
              <div className="text-left">
                <div className="font-medium">{zone.name}</div>
                <div className="text-xs opacity-75">{zone.userCount} users</div>
              </div>
            </Button>
          ))}
        </div>
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-b border-gray-800 overflow-hidden"
          >
            <div className="p-4 space-y-4">
              <h3 className="font-medium text-white">Filters</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button variant="outline" size="sm" className="border-gray-600 text-gray-300">
                  <Heart className="h-4 w-4 mr-2" />
                  Romantic
                </Button>
                <Button variant="outline" size="sm" className="border-gray-600 text-gray-300">
                  <Users className="h-4 w-4 mr-2" />
                  Platonic
                </Button>
                <Button variant="outline" size="sm" className="border-gray-600 text-gray-300">
                  <Activity className="h-4 w-4 mr-2" />
                  Active Now
                </Button>
                <Button variant="outline" size="sm" className="border-gray-600 text-gray-300">
                  <User className="h-4 w-4 mr-2" />
                  Units
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Map Area */}
      <div className="flex-1 relative">
        {/* Canvas for Bubble Grid */}
        <canvas
          ref={canvasRef}
          onClick={handleCanvasClick}
          className="absolute inset-0 cursor-pointer"
          style={{ width: '100%', height: '100%' }}
        />

        {/* Zoom Controls */}
        <div className="absolute bottom-4 right-4 flex flex-col space-y-2">
          <Button
            variant="outline"
            size="icon"
            onClick={handleZoomIn}
            className="bg-gray-900/80 border-gray-600 text-white hover:bg-gray-800"
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={handleZoomOut}
            className="bg-gray-900/80 border-gray-600 text-white hover:bg-gray-800"
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
        </div>

        {/* Zone Info */}
        {selectedZone && (
          <div className="absolute top-4 left-4">
            <Card className="impression-card">
              <CardContent className="p-4">
                <h3 className="font-medium text-white mb-2">{selectedZone.name}</h3>
                <div className="space-y-1 text-sm text-gray-300">
                  <div className="flex items-center justify-between">
                    <span>Active Users:</span>
                    <span className="text-pink-400">{selectedZone.userCount}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Density:</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 h-2 bg-gray-700 rounded-full overflow-hidden">
                        <div
                          className="h-full impression-gradient"
                          style={{ width: `${selectedZone.density * 100}%` }}
                        />
                      </div>
                      <span className="text-pink-400">{Math.round(selectedZone.density * 100)}%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Legend */}
        <div className="absolute bottom-4 left-4">
          <Card className="impression-card">
            <CardContent className="p-4">
              <h4 className="font-medium text-white mb-2 text-sm">Legend</h4>
              <div className="space-y-2 text-xs text-gray-300">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-pink-500"></div>
                  <span>Individual User</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <span>Unit Profile</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span>Active Now</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-purple-500 opacity-50"></div>
                  <span>High Compatibility</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Loading State */}
        {bubbleUsers.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Card className="impression-card">
              <CardContent className="p-6 text-center">
                <Map className="h-12 w-12 text-pink-400 mx-auto mb-4 animate-pulse" />
                <h3 className="text-white font-medium mb-2">Loading Resonance Map</h3>
                <p className="text-gray-400 text-sm">
                  Discovering connections in {selectedZone?.name}...
                </p>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="border-t border-gray-800 p-4">
        <div className="flex justify-center space-x-8">
          <Button
            variant="ghost"
            className="flex flex-col items-center space-y-1 text-pink-400"
          >
            <Map className="h-5 w-5" />
            <span className="text-xs">Map</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => navigate('/matches')}
            className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white"
          >
            <Heart className="h-5 w-5" />
            <span className="text-xs">Matches</span>
          </Button>
          <Button
            variant="ghost"
            className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white"
          >
            <MessageCircle className="h-5 w-5" />
            <span className="text-xs">Chat</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => navigate('/settings')}
            className="flex flex-col items-center space-y-1 text-gray-400 hover:text-white"
          >
            <User className="h-5 w-5" />
            <span className="text-xs">Profile</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ResonanceMap;
