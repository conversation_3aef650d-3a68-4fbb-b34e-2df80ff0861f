import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Device capability detection
export interface DeviceCapabilities {
  tier: 'basic' | 'standard' | 'advanced';
  hasCamera: boolean;
  hasMotionSensors: boolean;
  hasHighPrecisionTimer: boolean;
  batteryOptimized: boolean;
}

export function detectDeviceCapabilities(): DeviceCapabilities {
  // Handle test environment
  if (typeof window === 'undefined') {
    return {
      tier: 'standard',
      hasCamera: false,
      hasMotionSensors: false,
      hasHighPrecisionTimer: true,
      batteryOptimized: true,
      supportedFeatures: []
    };
  }

  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const hasCamera = 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices;
  const hasMotionSensors = 'DeviceMotionEvent' in window;
  const hasHighPrecisionTimer = 'performance' in window && 'now' in performance;

  // Determine tier based on capabilities
  let tier: 'basic' | 'standard' | 'advanced' = 'basic';

  if (hasCamera && hasMotionSensors && hasHighPrecisionTimer) {
    tier = 'advanced';
  } else if (hasHighPrecisionTimer) {
    tier = 'standard';
  }

  return {
    tier,
    hasCamera,
    hasMotionSensors,
    hasHighPrecisionTimer,
    batteryOptimized: isMobile
  };
}

// Location privacy utilities
export interface LocationZone {
  id: string;
  name: string;
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  center: {
    lat: number;
    lng: number;
  };
  minSize: number; // minimum area in km²
}

export function abstractLocation(lat: number, lng: number): LocationZone {
  // This is a simplified implementation - in production, use proper geographic zones
  const zoneSize = 0.01; // roughly 1km
  const zoneLat = Math.floor(lat / zoneSize) * zoneSize;
  const zoneLng = Math.floor(lng / zoneSize) * zoneSize;

  return {
    id: `zone_${zoneLat}_${zoneLng}`,
    name: `Zone ${Math.abs(zoneLat).toFixed(2)}°${lat >= 0 ? 'N' : 'S'}, ${Math.abs(zoneLng).toFixed(2)}°${lng >= 0 ? 'E' : 'W'}`,
    bounds: {
      north: zoneLat + zoneSize,
      south: zoneLat,
      east: zoneLng + zoneSize,
      west: zoneLng
    },
    center: {
      lat: zoneLat + zoneSize / 2,
      lng: zoneLng + zoneSize / 2
    },
    minSize: 1 // 1 km²
  };
}

// Behavioral vector utilities
export interface BehavioralVector {
  dimensions: number[];
  timestamp: number;
  deviceTier: 'basic' | 'standard' | 'advanced';
  confidence: number;
}

export function normalizeBehavioralVector(rawData: {
  dwellTime: number;
  scrollVelocity: number;
  returnCount: number;
  focusAreas: number[];
  interactionDepth: number;
}): BehavioralVector {
  // Normalize each dimension to 0-1 range
  const normalizedDwellTime = Math.min(rawData.dwellTime / 30000, 1); // 30 seconds max
  const normalizedScrollVelocity = Math.min(rawData.scrollVelocity / 1000, 1); // 1000px/s max
  const normalizedReturnCount = Math.min(rawData.returnCount / 5, 1); // 5 returns max
  const normalizedInteractionDepth = Math.min(rawData.interactionDepth / 10, 1); // 10 interactions max

  // Create 10-dimensional vector
  const dimensions = [
    normalizedDwellTime,
    normalizedScrollVelocity,
    normalizedReturnCount,
    normalizedInteractionDepth,
    ...rawData.focusAreas.slice(0, 6).map(area => Math.min(area, 1)) // up to 6 focus areas
  ];

  // Pad to 10 dimensions if needed
  while (dimensions.length < 10) {
    dimensions.push(0);
  }

  return {
    dimensions: dimensions.slice(0, 10),
    timestamp: Date.now(),
    deviceTier: detectDeviceCapabilities().tier,
    confidence: calculateVectorConfidence(rawData)
  };
}

function calculateVectorConfidence(rawData: any): number {
  // Simple confidence calculation based on data completeness
  let confidence = 0.5; // base confidence

  if (rawData.dwellTime > 1000) confidence += 0.1;
  if (rawData.scrollVelocity > 0) confidence += 0.1;
  if (rawData.returnCount > 0) confidence += 0.1;
  if (rawData.focusAreas.length > 0) confidence += 0.2;

  return Math.min(confidence, 1);
}

// Vector similarity calculation
export function calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
  if (vectorA.length !== vectorB.length) {
    throw new Error('Vectors must have the same length');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < vectorA.length; i++) {
    dotProduct += vectorA[i] * vectorB[i];
    normA += vectorA[i] * vectorA[i];
    normB += vectorB[i] * vectorB[i];
  }

  if (normA === 0 || normB === 0) {
    return 0;
  }

  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

// Dynamic threshold calculation
export function calculateDynamicThreshold(
  basethreshold: number,
  localDensity: number,
  medianDensity: number
): number {
  const densityRatio = localDensity / medianDensity;
  const adjustment = Math.log(densityRatio + 1) * 0.1; // logarithmic scaling
  const adjustedThreshold = basethreshold * (1 + adjustment);

  // Clamp between 0.65 and 0.85
  return Math.max(0.65, Math.min(0.85, adjustedThreshold));
}

// Privacy-preserving utilities
export function generateAnonymousId(): string {
  return 'anon_' + Math.random().toString(36).substr(2, 9);
}

export function hashUserInteraction(userId: string, targetId: string): string {
  // Simple hash for demo - use proper cryptographic hash in production
  return btoa(`${userId}_${targetId}_${Date.now()}`);
}

// Time utilities
export function formatTimeRemaining(milliseconds: number): string {
  const hours = Math.floor(milliseconds / (1000 * 60 * 60));
  const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
}

// Validation utilities
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
