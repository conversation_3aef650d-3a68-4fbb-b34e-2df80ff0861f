import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, PrivacySettings } from '@/types';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
  updatePrivacySettings: (settings: Partial<PrivacySettings>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Default privacy settings for new users
  const defaultPrivacySettings: PrivacySettings = {
    locationPrecision: 'zone',
    behavioralTracking: true,
    voiceAnalysis: true,
    photoAnalysis: true,
    dataRetention: 30,
    anonymousMode: false,
  };

  // Mock authentication functions - replace with Firebase/Supabase
  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock user data
      const mockUser: User = {
        id: '1',
        email,
        name: 'Demo User',
        photos: [],
        connectionIntents: [],
        onboardingComplete: false,
        privacySettings: defaultPrivacySettings,
        createdAt: new Date(),
        lastActive: new Date(),
      };
      
      setUser(mockUser);
      localStorage.setItem('impression_user', JSON.stringify(mockUser));
    } catch (error) {
      throw new Error('Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, name: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newUser: User = {
        id: Date.now().toString(),
        email,
        name,
        photos: [],
        connectionIntents: [],
        onboardingComplete: false,
        privacySettings: defaultPrivacySettings,
        createdAt: new Date(),
        lastActive: new Date(),
      };
      
      setUser(newUser);
      localStorage.setItem('impression_user', JSON.stringify(newUser));
    } catch (error) {
      throw new Error('Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setUser(null);
    localStorage.removeItem('impression_user');
  };

  const updateUser = async (userData: Partial<User>): Promise<void> => {
    if (user) {
      const updatedUser = { 
        ...user, 
        ...userData,
        lastActive: new Date()
      };
      setUser(updatedUser);
      localStorage.setItem('impression_user', JSON.stringify(updatedUser));
    }
  };

  const updatePrivacySettings = async (settings: Partial<PrivacySettings>): Promise<void> => {
    if (user) {
      const updatedPrivacySettings = {
        ...user.privacySettings,
        ...settings
      };
      
      const updatedUser = {
        ...user,
        privacySettings: updatedPrivacySettings,
        lastActive: new Date()
      };
      
      setUser(updatedUser);
      localStorage.setItem('impression_user', JSON.stringify(updatedUser));
    }
  };

  // Check for existing session on mount
  useEffect(() => {
    const checkAuthState = () => {
      try {
        const storedUser = localStorage.getItem('impression_user');
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          // Convert date strings back to Date objects
          parsedUser.createdAt = new Date(parsedUser.createdAt);
          parsedUser.lastActive = new Date(parsedUser.lastActive);
          setUser(parsedUser);
        }
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('impression_user');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthState();
  }, []);

  // Update last active time periodically
  useEffect(() => {
    if (user) {
      const interval = setInterval(() => {
        updateUser({ lastActive: new Date() });
      }, 60000); // Update every minute

      return () => clearInterval(interval);
    }
  }, [user]);

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    updateUser,
    updatePrivacySettings,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
